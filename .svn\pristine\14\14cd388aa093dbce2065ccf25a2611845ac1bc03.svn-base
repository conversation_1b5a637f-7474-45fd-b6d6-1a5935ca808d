<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Seo extends Common
{
    public function index(){
        $keyword = input('keyword', '');
        $where = [];

        if ($keyword) {
            $where[] = ['name', 'like', '%' . $keyword . '%'];
        }

        $List = Db::name('seo')
            ->where($where)
            ->order("sort asc, id desc")
            ->paginate([
                'query'     =>  request()->param(),
                'list_rows' => 20,
            ]);

        return view("seo/index", [
            "List" => $List,
            "keyword" => $keyword
        ]);
    }

    public function add(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("SEO名称不能为空！");
            }

            // 处理文件上传
            if(isset($_FILES['image']) && $_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));

            $s = Db::name("seo")->insertGetId($data);
            if($s){
                $this->success('添加成功');
            } else {
                $this->error("添加失败，请重试");
            }
        } else {
            // 获取父级SEO列表
            $parentSeos = Db::name('seo')->where('parent_id', 0)->field('id, name')->select();

            return view("seo/add", [
                "parentSeos" => $parentSeos
            ]);
        }
    }

    public function edit(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("SEO名称不能为空！");
            }

            // 处理文件上传
            if(isset($_FILES['image']) && $_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));

            $s = Db::name("seo")->where("id", $data['id'])->save($data);
            if($s) {
                $this->success('修改成功');
            } else {
                $this->error("修改失败，请重试！");
            }

        } else {
            $id = input('id');
            $getone = Db::name("seo")->where("id", $id)->find();

            if (!$getone) {
                $this->error("SEO记录不存在");
            }

            // 获取父级SEO列表（排除自己）
            $parentSeos = Db::name('seo')
                ->where('parent_id', 0)
                ->where('id', '<>', $id)
                ->field('id, name')
                ->select();

            return view("seo/edit", [
                "getone" => $getone,
                "parentSeos" => $parentSeos
            ]);
        }
    }

    public function del(){
        $id = input('id');

        // 检查是否有子级SEO
        $hasChildren = Db::name("seo")->where("parent_id", $id)->count();
        if ($hasChildren > 0) {
            $this->error("该SEO设置下还有子级设置，请先删除子级设置");
        }

        $s = Db::name("seo")->where("id", $id)->delete();
        if($s){
            $this->success("删除成功");
        }else{
            $this->error("删除失败");
        }
    }
}
