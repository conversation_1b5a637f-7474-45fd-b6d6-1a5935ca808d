/**
 * CKEditor5 封装工具类
 * 提供统一的富文本编辑器初始化和管理功能
 */

class CKEditorHelper {
    constructor() {
        this.editors = new Map(); // 存储编辑器实例
        this.plugins = {}; // 初始化为空对象
        this.initializePlugins(); // 初始化插件引用
    }

    /**
     * 获取默认配置
     * 延迟初始化，确保插件已加载
     */
    getDefaultConfig() {
        return {
            // 许可证配置 - 根据您的情况选择一个
            // licenseKey: 'GPL', // 如果是开源项目使用GPL
            licenseKey: 'eyJhbGciOiJFUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************.PARmfYm77OpEgYqd-P7V_9vFTLJ1d_Bglwe7bd0FUHSjQ4vFm7uR9tWCuSbabhAu0wHn3_-bsP2RuiCIELqYcg', // 如果有商业许可证，请替换这里
            // licenseKey: '', // 空字符串有时也能工作
            // 如果上面都不行，可以尝试注释掉licenseKey行

            language: 'zh-cn',
            toolbar: {
                items: [
                    'heading', '|',
                    'sourceEditing', '|',
                    'bold', 'italic', 'underline', '|',
                    'fontSize','fontFamily','fontColor', 'fontBackgroundColor', '|',
                    'bulletedList', 'numberedList', '|',
                    'outdent', 'indent', '|',
                    'link', 'insertImage', '|',
                    'insertTable', 'highlight','blockQuote', '|',
                    'alignment', '|',
                    'undo', 'redo'
                ]
            },
            // 动态添加可用插件
            plugins: this.getDynamicPlugins(),
            image: this.getImageConfig(),
            // 添加简单上传适配器配置
            simpleUpload: {
                uploadUrl: '/admin/editorImage',
                allowedTypes: ['jpeg', 'jpg', 'png', 'gif', 'webp'],
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            }
        };
    }

    /**
     * 初始化插件引用
     * 从window.CKEDITOR中解构所需的插件
     */
    initializePlugins() {
        if (!window.CKEDITOR) {
            console.error('window.CKEDITOR 未找到，请确保CKEditor5 UMD文件已正确加载');
            this.plugins = {};
            return;
        }

        // 从window.CKEDITOR中解构所需的插件
        const {
            ClassicEditor,
            Alignment,
            Autoformat,
            AutoImage,
            AutoLink,
            Autosave,
            BlockQuote,
            Bold,
            Bookmark,
            Code,
            CodeBlock,
            Emoji,
            Essentials,
            FindAndReplace,
            FontBackgroundColor,
            FontColor,
            FontFamily,
            FontSize,
            FullPage,
            Fullscreen,
            GeneralHtmlSupport,
            Heading,
            Highlight,
            HorizontalLine,
            HtmlComment,
            HtmlEmbed,
            ImageBlock,
            ImageCaption,
            ImageEditing,
            ImageInline,
            ImageInsert,
            ImageInsertViaUrl,
            ImageResize,
            ImageStyle,
            ImageTextAlternative,
            ImageToolbar,
            ImageUpload,
            ImageUtils,
            Indent,
            IndentBlock,
            Italic,
            Link,
            LinkImage,
            List,
            ListProperties,
            Markdown,
            MediaEmbed,
            Mention,
            PageBreak,
            Paragraph,
            PasteFromMarkdownExperimental,
            PasteFromOffice,
            PlainTableOutput,
            RemoveFormat,
            ShowBlocks,
            SimpleUploadAdapter,
            SourceEditing,
            SpecialCharacters,
            SpecialCharactersArrows,
            SpecialCharactersCurrency,
            SpecialCharactersEssentials,
            SpecialCharactersLatin,
            SpecialCharactersMathematical,
            SpecialCharactersText,
            Strikethrough,
            Style,
            Subscript,
            Superscript,
            Table,
            TableCaption,
            TableCellProperties,
            TableColumnResize,
            TableLayout,
            TableProperties,
            TableToolbar,
            TextPartLanguage,
            TextTransformation,
            Title,
            TodoList,
            Underline,
            WordCount
        } = window.CKEDITOR;

        // 存储插件引用
        this.plugins = {
            ClassicEditor,
            Alignment,
            Autoformat,
            AutoImage,
            AutoLink,
            Autosave,
            BlockQuote,
            Bold,
            Bookmark,
            Code,
            CodeBlock,
            Emoji,
            Essentials,
            FindAndReplace,
            FontBackgroundColor,
            FontColor,
            FontFamily,
            FontSize,
            FullPage,
            Fullscreen,
            GeneralHtmlSupport,
            Heading,
            Highlight,
            HorizontalLine,
            HtmlComment,
            HtmlEmbed,
            ImageBlock,
            ImageCaption,
            ImageEditing,
            ImageInline,
            ImageInsert,
            ImageInsertViaUrl,
            ImageResize,
            ImageStyle,
            ImageTextAlternative,
            ImageToolbar,
            ImageUpload,
            ImageUtils,
            Indent,
            IndentBlock,
            Italic,
            Link,
            LinkImage,
            List,
            ListProperties,
            Markdown,
            MediaEmbed,
            Mention,
            PageBreak,
            Paragraph,
            PasteFromMarkdownExperimental,
            PasteFromOffice,
            PlainTableOutput,
            RemoveFormat,
            ShowBlocks,
            SimpleUploadAdapter,
            SourceEditing,
            SpecialCharacters,
            SpecialCharactersArrows,
            SpecialCharactersCurrency,
            SpecialCharactersEssentials,
            SpecialCharactersLatin,
            SpecialCharactersMathematical,
            SpecialCharactersText,
            Strikethrough,
            Style,
            Subscript,
            Superscript,
            Table,
            TableCaption,
            TableCellProperties,
            TableColumnResize,
            TableLayout,
            TableProperties,
            TableToolbar,
            TextPartLanguage,
            TextTransformation,
            Title,
            TodoList,
            Underline,
            WordCount
        };

        // console.log('已初始化的插件:', Object.keys(this.plugins).filter(key => this.plugins[key]));
    }



    /**
     * 获取动态插件配置
     * @returns {Array} 可用的插件数组
     */
    getDynamicPlugins() {
        const plugins = [];

        // 基础必需插件
        const essentialPlugins = [
            'Essentials',
            'Paragraph',
            'Autoformat',
            'TextTransformation',
            'AutoLink',
            'PasteFromOffice'
        ];

        // 工具栏相关插件
        const toolbarPlugins = [
            'Heading',
            'Bold',
            'Italic',
            'Underline',
            'FontColor',
            'FontBackgroundColor',
            'List',
            'ListProperties',
            'Indent',
            'IndentBlock',
            'Link',
            'LinkImage',
            'BlockQuote',
            'Table',
            'TableToolbar',
            'TableProperties',
            'TableCellProperties',
            'TableColumnResize',
            'TableLayout',
            'TableCaption'
        ];

        // 图片相关插件
        const imagePlugins = [
            'ImageBlock',
            'ImageCaption',
            'ImageEditing',
            'ImageInline',
            'ImageInsert',
            'ImageInsertViaUrl',
            'ImageResize',
            'ImageStyle',
            'ImageTextAlternative',
            'ImageToolbar',
            'ImageUpload',
            'ImageUtils',
            'AutoImage'
        ];

        // 上传和其他功能插件
        const additionalPlugins = [
            'SimpleUploadAdapter',
            'RemoveFormat',
            'GeneralHtmlSupport'
        ];

        // 合并所有插件列表
        const allPluginNames = [
            ...essentialPlugins,
            ...toolbarPlugins,
            ...imagePlugins,
            ...additionalPlugins
        ];

        // 添加可用的插件
        allPluginNames.forEach(pluginName => {
            if (this.plugins[pluginName]) {
                plugins.push(this.plugins[pluginName]);
                console.log(`添加插件: ${pluginName}`);
            } else {
                console.warn(`插件不可用: ${pluginName}`);
            }
        });

        return plugins;
    }

    /**
     * 获取图片配置
     * @returns {Object} 图片配置对象
     */
    getImageConfig() {
        const config = {
            toolbar: [
                'toggleImageCaption',
                'imageTextAlternative',
                '|',
                'imageStyle:inline',
                'imageStyle:block',
                'imageStyle:wrapText',
                'imageStyle:breakText',
                'imageStyle:side',
                '|',
                'resizeImage'
            ],
            upload: {
                types: ['jpeg', 'jpg', 'png', 'gif', 'webp']
            }
        };

        return config;
    }

    /**
     * 初始化编辑器
     * @param {string|Element} selector - 选择器或DOM元素
     * @param {Object} options - 配置选项
     * @returns {Promise} 编辑器实例
     */
    async init(selector, options = {}) {
        const element = typeof selector === 'string' ?
            document.querySelector(selector) : selector;

        if (!element) {
            throw new Error(`编辑器元素未找到: ${selector}`);
        }

        // 合并配置
        const config = {
            ...this.getDefaultConfig(),
            ...options,
            extraPlugins: [
                ...(options.extraPlugins || [])
            ]
        };

        console.log('使用的许可证配置:', config.licenseKey);

        try {
            console.log('初始化CKEditor5:', element);

            // 使用从window.CKEDITOR解构的ClassicEditor
            if (!this.plugins.ClassicEditor) {
                console.error('ClassicEditor 未找到，请检查CKEditor5 UMD文件是否正确加载');
                throw new Error('ClassicEditor 未找到，请检查CKEditor5是否正确加载');
            }

            console.log('使用 window.CKEDITOR.ClassicEditor');
            const editor = await this.plugins.ClassicEditor.create(element, config);

            // 存储编辑器实例
            const editorName = element.getAttribute('data-name') ||
                              element.id ||
                              `editor_${this.editors.size}`;
            this.editors.set(editorName, editor);

            // 设置初始内容
            const initialContent = element.getAttribute('data-content') ||
                                 element.getAttribute('data-initial-content') || '';
            if (initialContent) {
                editor.setData(initialContent);
            }

            console.log(`CKEditor5 "${editorName}" 初始化成功`);
            return editor;
        } catch (error) {
            console.error('CKEditor5 初始化失败:', error);
            throw error;
        }
    }

    /**
     * 批量初始化编辑器
     * @param {string} selector - 选择器
     * @param {Object} options - 配置选项
     */
    async initAll(selector = '.ckeditor-textarea', options = {}) {
        const elements = document.querySelectorAll(selector);
        const promises = [];

        elements.forEach(element => {
            promises.push(this.init(element, options));
        });

        try {
            const editors = await Promise.all(promises);
            console.log(`批量初始化完成，共 ${editors.length} 个编辑器`);
            return editors;
        } catch (error) {
            console.error('批量初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取编辑器实例
     * @param {string} name - 编辑器名称
     * @returns {Object|null} 编辑器实例
     */
    getEditor(name) {
        return this.editors.get(name) || null;
    }

    /**
     * 获取编辑器内容
     * @param {string} name - 编辑器名称
     * @returns {string} 编辑器内容
     */
    getData(name) {
        const editor = this.getEditor(name);
        return editor ? editor.getData() : '';
    }

    /**
     * 设置编辑器内容
     * @param {string} name - 编辑器名称
     * @param {string} data - 内容
     */
    setData(name, data) {
        const editor = this.getEditor(name);
        if (editor) {
            editor.setData(data);
        }
    }

    /**
     * 销毁编辑器
     * @param {string} name - 编辑器名称
     */
    destroy(name) {
        const editor = this.getEditor(name);
        if (editor) {
            editor.destroy();
            this.editors.delete(name);
            console.log(`编辑器 "${name}" 已销毁`);
        }
    }

    /**
     * 销毁所有编辑器
     */
    destroyAll() {
        this.editors.forEach((editor, name) => {
            editor.destroy();
            console.log(`编辑器 "${name}" 已销毁`);
        });
        this.editors.clear();
    }

    /**
     * 获取所有编辑器的内容
     * @returns {Object} 编辑器名称和内容的映射
     */
    getAllData() {
        const data = {};
        this.editors.forEach((editor, name) => {
            // 强制刷新编辑器内容，确保获取最新状态
            try {
                // 触发编辑器的change事件，确保内容同步
                editor.model.document.fire('change:data');
                const content = editor.getData();
                data[name] = content;
                console.log(`编辑器 "${name}" 内容已获取，长度: ${content.length}`);
            } catch (error) {
                console.error(`获取编辑器 "${name}" 内容时出错:`, error);
                data[name] = editor.getData(); // 降级处理
            }
        });
        return data;
    }

    /**
     * 强制刷新所有编辑器内容
     * 确保图片大小调整等DOM变化被正确捕获
     */
    refreshAllEditors() {
        this.editors.forEach((editor, name) => {
            try {
                // 获取编辑器的可编辑区域
                const editableElement = editor.ui.view.editable.element;

                // 触发input事件，让编辑器重新扫描DOM变化
                const inputEvent = new Event('input', { bubbles: true });
                editableElement.dispatchEvent(inputEvent);

                // 手动触发change事件
                editor.model.document.fire('change:data');

                console.log(`编辑器 "${name}" 已刷新`);
            } catch (error) {
                console.error(`刷新编辑器 "${name}" 时出错:`, error);
            }
        });
    }
}



// 创建全局实例
window.CKEditorHelper = CKEditorHelper;
window.ckEditorHelper = new CKEditorHelper();

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.ckEditorHelper) {
        window.ckEditorHelper.destroyAll();
    }
});

console.log('CKEditor Helper 加载完成');
