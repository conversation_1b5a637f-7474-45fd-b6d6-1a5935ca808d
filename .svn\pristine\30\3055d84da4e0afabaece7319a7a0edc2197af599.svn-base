<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    {include file="public:head"}
</head>
<body class="pt-[50px] md:pt-[100px]">
    {include file="public:header"}

    <section class="bg-[#f7f6fa] h-14 md:h-20 mb-5">
        <div class="max-w-9/10 mx-auto container h-full flex items-center">
            <!-- 面包屑导航 -->
            <nav class="breadcrumb-nav">
                <ol
                    class="flex items-center before:content-['\25B6'] before:text-[#001045] before:mr-1 before:text-[10px] gap-x-2 uppercase">
                    <li class=" flex items-center gap-x-2">
                        <a href="/" class="text-[#001045]">HOME</a> \
                    </li>
                    <li class=" flex items-center gap-x-2">
                        <a href="/faq.html" class="text-[#001045]">faq</a> \
                    </li>
                    <li class="flex items-center max-w-[100px] md:max-w-2xs">
                        <span class="text-[#999] line-clamp-1">{$getone.question}</span>
                    </li>
                </ol>
            </nav>

        </div>
    </section>

    <section class="details-container mb-5  md:mt-14">
        <div class="max-w-9/10 mx-auto container">
            <div class="bg-[#ecf3f7] p-4">
                <div class=" md:px-8 md:py-12 md:max-w-9/10 md:mx-auto md:container">
                    <div class="details-title mb-3">
                        <h1 class="details-title-h1 text-3xl md:text-5xl bold-text text-[#001045] mb-4">
                            {$getone.question}
                        </h1>
                        <!-- 日期 -->
                        <div
                            class="details-date text-[#666] text-sm border-b-[1px] border-[#ddd] pb-2.5 flex flex-row items-center gap-x-4 md:text-base">
                            <span>{$getone.publish_date}</span>
                            <span>EDITOR：{$getone.publisher} </span>
                        </div>
                    </div>

                    <div class="details-content max-w-full md:min-h-lvh">
                        <p>
                            {$getone.answer|raw}
                        </p>
                    </div>
                </div>
            </div>

            <div class="details-footer flex flex-col md:flex-row items-start mt-5 mb-5 md:mt-14 md:items-center md:justify-between">
                <div class="flex flex-col mb-5 md:text-xl">
                    {if condition="$prev"}
                    <div class="details-footer-previous line-clamp-1 text-[#001045] mb-2.5">
                        Previous post: <a href="/faq/{$prev.seo_url}.html" class="text-[#2479ff] underline">{$prev.question|strip_tags}</a>
                    </div>
                    {/if}
                    {if condition="$next"}
                    <div class="details-footer-next line-clamp-1 text-[#001045]">
                        Next chapter: <a href="/faq/{$next.seo_url}.html" class="text-[#2479ff] underline">{$next.question|strip_tags}</a>
                    </div>
                    {/if}
                </div>
                <div class="bg-[#2479ff] w-auto h-8 rounded-sm md:w-[260px] md:h-[60px]">
                    <a href="/faq/" class="text-white flex flex-row items-center gap-x-2.5 h-full px-5 py-3 justify-center md:gap-x-5">
                        <span class="text-sm md:text-xl">Return list</span>
                        <svg t="1740466662370" class="w-[20px] h-[20px] md:w-[26px] md:h-[26px]" viewBox="0 0 1024 1024" version="1.1"
                            xmlns="http://www.w3.org/2000/svg" p-id="1727">
                            <path
                                d="M569.664 352.896c130.986667 0 237.162667 106.176 237.162667 237.162667 0 130.986667-106.176 237.184-237.162667 237.184H213.909333v79.061333h355.754667c174.656 0 316.224-141.589333 316.224-316.245333 0-174.634667-141.568-316.224-316.224-316.224h-283.52l100.266667-100.245334-55.893334-55.893333L134.826667 313.386667l195.669333 195.669333 55.893333-55.893333-100.266666-100.245334h283.52z"
                                fill="#ffffff" p-id="1728"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <section class="md:pb-16 md:pt-5">
        <div class="max-w-9/10 mx-auto container">
            <div class="overseas-marketing-services flex flex-col md:flex-row md:justify-between md:items-center">
                <div class="mb-3.5">
                    <h2 class="text-2xl bold-text mb-2.5 md:text-5xl">
                        Overseas <span class="text-[#2ee742]">Marketing Services</span>
                    </h2>
                    <p class="text-base text-[#666666] md:text-base">
                        Expand your global reach with our targeted international marketing solutions.
                    </p>
                </div>
            </div>
            <div class="swiper OverseasSwiper mt-2">
                <div class="swiper-wrapper pt-7">
                    {volist name="services" id="vo"}
                    <div class="swiper-slide">
                        <div class="w-full h-full bg-[#fff] rounded-sm transition-all border border-[#dddddd] hover:border-[#fff]">
                            <a href="/service/{$vo.seo_url}" class="block h-full px-6 pt-6 pb-8">
                                <div class="flex flex-row items-center gap-x-2 min-h-14 overflow-hidden md:mb-3.5 md:gap-x-4">
                                    <img src="{$vo.icon}" alt="services_1" class="w-8 h-8 md:w-auto md:h-auto">
                                    <h3 class="text-base bold-text md:text-2xl line-clamp-2">{$vo.name}</h3>
                                </div>
                                <div class="overflow-hidden min-h-24 mb-8">
                                    <p class="text-base line-clamp-4 text-[#666666] hyphens-manual">
                                        {$vo.content|strip_tags}
                                    </p>
                                </div>
                            </a>
                        </div>
                    </div>
                    {/volist}
                </div>
                <!-- <div class="swiper-pagination overseas-swiper bottom-0 md:hidden"></div> -->
            </div>
        </div>
    </section>

    {include file="public:footer"}
    {include file="public:foot"}

    <script>

        var swiper = new Swiper(".OverseasSwiper", {
            slidesPerView: "auto",
            spaceBetween: 20, //两者之间的间距
            slidesPerView: 1.3,
            pagination: {
                el: ".overseas-swiper",
                clickable: true,
            },
            breakpoints: {
                768: {
                    slidesPerView: 4,
                }
            }
        });
    </script>
</body>

</html>