<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>添加FAQ</title>
    {include file="common/head" /}
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>添加FAQ</span>
                <el-button style="float: right;" @click="goBack">返回列表</el-button>
            </div>

            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="FAQ标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入FAQ标题"></el-input>
                </el-form-item>

                <el-form-item label="SEO URL" prop="seo_url">
                    <el-input v-model="form.seo_url" placeholder="留空自动生成"></el-input>
                </el-form-item>

                <el-form-item label="关联服务" prop="service_id">
                    <el-select v-model="form.service_id" placeholder="请选择关联服务">
                        <el-option
                            label="不关联任何服务"
                            :value="0">
                        </el-option>
                        <el-option
                            v-for="service in services"
                            :key="service.id"
                            :label="service.name"
                            :value="service.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="发布日期" prop="publish_date">
                    <el-date-picker
                        v-model="form.publish_date"
                        type="date"
                        placeholder="选择发布日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>

                <el-form-item label="作者" prop="publisher">
                    <el-input v-model="form.publisher" placeholder="请输入作者"></el-input>
                </el-form-item>

                <el-form-item label="FAQ图片">
                    <div class="file-upload-container">
                        <input type="file" name="image" accept="image/*" style="display: none;">
                        <el-button class="upload-btn" size="small" type="primary">选择图片</el-button>
                        <span style="margin-left: 10px; color: #999;">支持jpg、png、gif格式，非必填</span>
                        <div class="file-preview" style="margin-top: 10px;">
                            <img v-if="form.imagePreview" :src="form.imagePreview" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                    </div>
                </el-form-item>

                <el-form-item label="推荐">
                    <el-switch v-model="form.is_recommend"></el-switch>
                </el-form-item>

                <el-form-item label="FAQ内容">
                    <div class="ckeditor-textarea" data-name="content"></div>
                </el-form-item>

                <!-- SEO信息 -->
                <div class="form-section">
                    <div class="section-title">SEO信息</div>

                    <el-form-item label="SEO标题">
                        <el-input v-model="form.seo_title" placeholder="页面标题，留空则使用FAQ标题" maxlength="60" show-word-limit></el-input>
                        <div style="margin-top: 5px; color: #999; font-size: 12px;">
                            建议长度：50-60个字符，用于搜索引擎显示
                        </div>
                    </el-form-item>

                    <el-form-item label="SEO描述">
                        <el-input type="textarea" v-model="form.seo_description" placeholder="页面描述，用于搜索引擎摘要显示"
                                  :rows="3" maxlength="160" show-word-limit></el-input>
                        <div style="margin-top: 5px; color: #999; font-size: 12px;">
                            建议长度：120-160个字符，简洁描述页面内容
                        </div>
                    </el-form-item>

                    <el-form-item label="SEO关键词">
                        <el-input v-model="form.seo_keywords" placeholder="关键词，用英文逗号分隔，如：常见问题,FAQ,帮助"></el-input>
                        <div style="margin-top: 5px; color: #999; font-size: 12px;">
                            建议3-5个关键词，用英文逗号分隔
                        </div>
                    </el-form-item>
                </div>

                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="submitting">提交</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    submitting: false,
                    services: <?php echo json_encode($services); ?>,
                    form: {
                        title: '',
                        seo_url: '',
                        service_id: 0,
                        content: '',
                        image: '',
                        imagePreview: '',
                        seo_title: '',
                        seo_description: '',
                        seo_keywords: '',
                        publish_date: '',
                        publisher: '',
                        is_recommend: false
                    },
                    rules: {
                        title: [
                            { required: true, message: '请输入FAQ标题', trigger: 'blur' }
                        ],
                        publish_date: [
                            { required: true, message: '请选择发布日期', trigger: 'change' }
                        ]
                    }
                }
            },
            mounted() {
                // 设置默认发布日期为今天
                this.form.publish_date = new Date().toISOString().split('T')[0];
            },
            methods: {
                submitForm() {
                    window.AdminUtils.submitForm(this, {
                        url: "{:url('add')}",
                        formData: this.form,
                        fileFields: ['image'],
                        redirectUrl: "{:url('index')}",
                    });
                },
                goBack() {
                    window.AdminUtils.goBack("{:url('index')}");
                }
            }
        });
    </script>
</body>
</html>
