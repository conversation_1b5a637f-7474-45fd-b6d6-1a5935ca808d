<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>编辑SEO设置</title>
    {include file="common/head" /}
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>编辑SEO设置</span>
                <el-button style="float: right;" @click="goBack">返回列表</el-button>
            </div>

            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="SEO名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入SEO名称"></el-input>
                </el-form-item>

                <el-form-item label="页面标题" prop="seo_title">
                    <el-input v-model="form.seo_title" placeholder="请输入页面标题"></el-input>
                </el-form-item>

                <el-form-item label="关键词" prop="seo_keywords">
                    <el-input v-model="form.seo_keywords" placeholder="请输入关键词，多个关键词用逗号分隔"></el-input>
                </el-form-item>

                <el-form-item label="描述" prop="seo_description">
                    <el-input
                        type="textarea"
                        v-model="form.seo_description"
                        placeholder="请输入页面描述"
                        :rows="4">
                    </el-input>
                </el-form-item>

                <el-form-item label="SEO图片">
                    <div class="file-upload-container icon-preview">
                        <input type="file" name="image" accept="image/*">
                        <button type="button" class="upload-btn">选择图片</button>
                        <div class="file-preview">
                            <img v-if="form.image" :src="form.image" class="preview-image" style="display: block;">
                        </div>
                    </div>
                </el-form-item>

                <el-form-item label="父级SEO">
                    <el-select v-model="form.parent_id" placeholder="请选择父级SEO（可选）" clearable>
                        <el-option label="无父级" :value="0"></el-option>
                        <el-option
                            v-for="parent in parentSeos"
                            :key="parent.id"
                            :label="parent.name"
                            :value="parent.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="排序">
                    <el-input-number v-model="form.sort" :min="0" :max="999"></el-input-number>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="submitting">提交</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    submitting: false,
                    parentSeos: <?php echo json_encode($parentSeos); ?>,
                    form: {
                        id: <?php echo $getone['id']; ?>,
                        name: '<?php echo $getone['name'] ?? ''; ?>',
                        seo_title: '<?php echo $getone['seo_title'] ?? ''; ?>',
                        seo_keywords: '<?php echo $getone['seo_keywords'] ?? ''; ?>',
                        seo_description: '<?php echo $getone['seo_description'] ?? ''; ?>',
                        parent_id: <?php echo $getone['parent_id'] ?? 0; ?>,
                        sort: <?php echo $getone['sort'] ?? 50; ?>,
                        image: '<?php echo $getone['image'] ?? ''; ?>'
                    },
                    rules: {
                        name: [
                            { required: true, message: '请输入SEO名称', trigger: 'blur' }
                        ]
                    }
                }
            },
            methods: {
                submitForm() {
                    window.AdminUtils.submitForm(this, {
                        url: "{:url('edit')}",
                        formData: this.form,
                        fileFields: ['image'],
                        redirectUrl: "{:url('index')}",
                    });
                },
                goBack() {
                    window.AdminUtils.goBack("{:url('index')}");
                }
            }
        });
    </script>
</body>
</html>
