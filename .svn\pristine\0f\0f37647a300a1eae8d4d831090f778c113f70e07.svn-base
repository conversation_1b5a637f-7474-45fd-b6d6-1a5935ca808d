<?php return array(
    'root' => array(
        'name' => 'topthink/think',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => NULL,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'league/flysystem' => array(
            'pretty_version' => '1.1.10',
            'version' => '1.1.10.0',
            'reference' => '3239285c825c152bcc315fe0e87d6b55f5972ed1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-cached-adapter' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => 'd1925efb2207ac4be3ad0c40b8277175f99ffaff',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-cached-adapter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.16.0',
            'version' => '1.16.0.0',
            'reference' => '2d6702ff215bf922936ccc1ad31007edc76451b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'liliuwei/thinkphp-jump' => array(
            'pretty_version' => 'v2.0',
            'version' => '2.0.0.0',
            'reference' => '27a6d8da1c8d4f5734e0ce05f482ac07c568769c',
            'type' => 'think-extend',
            'install_path' => __DIR__ . '/../liliuwei/thinkphp-jump',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpmailer/phpmailer' => array(
            'pretty_version' => 'v6.9.3',
            'version' => '6.9.3.0',
            'reference' => '2f5c94fe7493efc213f643c23b1b1c249d40f47e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmailer/phpmailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => 'eb980457fa6899840fe1687e8627a03a7d8a3d52',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'topthink/framework' => array(
            'pretty_version' => 'v8.1.1',
            'version' => '8.1.1.0',
            'reference' => 'd301115ea77f33745b8d6659c74740c34fe53e9b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => NULL,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-captcha' => array(
            'pretty_version' => 'v3.0.11',
            'version' => '3.0.11.0',
            'reference' => '4f24f560a31011329e3d144732e5370d7676b3fb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-captcha',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-filesystem' => array(
            'pretty_version' => 'v2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'e8e51adb9f3a3f3aac2aa3ef73b7b439100f777d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-helper' => array(
            'pretty_version' => 'v3.1.10',
            'version' => '3.1.10.0',
            'reference' => 'ac66cc0859a12cd5d73258f50f338aadc95e9b46',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-helper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-multi-app' => array(
            'pretty_version' => 'v1.1.1',
            'version' => '1.1.1.0',
            'reference' => 'f93c604d5cfac2b613756273224ee2f88e457b88',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-multi-app',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-orm' => array(
            'pretty_version' => 'v3.0.33',
            'version' => '3.0.33.0',
            'reference' => '6e0ea679f7448ff9c8906606462505597681e22e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-orm',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-template' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '0b88bd449f0f7626dd75b05f557c8bc208c08b0c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-trace' => array(
            'pretty_version' => 'v1.6',
            'version' => '1.6.0.0',
            'reference' => '136cd5d97e8bdb780e4b5c1637c588ed7ca3e142',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-trace',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'topthink/think-view' => array(
            'pretty_version' => 'v2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'e6a68e4179baf8d795700d62b710da86d7c3f783',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-view',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wenhainan/thinkphp6-auth' => array(
            'pretty_version' => '1.1.2',
            'version' => '1.1.2.0',
            'reference' => '0fc5d4f9c02bc5bdc6629ca78f5f588884d0cdfe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wenhainan/thinkphp6-auth',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
