<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>添加策略</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <style>
        .strategy-container { padding: 20px; }
        .ck-editor__editable { min-height: 200px; }
    </style>
</head>
<body>
    <div id="app" class="strategy-container">
        <el-card>
            <div slot="header">
                <span>添加策略</span>
                <el-button style="float: right;" @click="goBack">返回列表</el-button>
            </div>

            <el-form ref="strategyForm" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="策略标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入策略标题"></el-input>
                </el-form-item>

                <el-form-item label="排序">
                    <el-input-number v-model="form.sort" :min="0" :max="999"></el-input-number>
                </el-form-item>

                <el-form-item label="策略内容">
                    <div id="content-editor"></div>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="submitting">提交</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    submitting: false,
                    contentEditor: null,
                    form: {
                        title: '',
                        content: '',
                        sort: 50
                    },
                    rules: {
                        title: [
                            { required: true, message: '请输入策略标题', trigger: 'blur' }
                        ]
                    }
                }
            },
            mounted() {
                this.initContentEditor();
            },
            methods: {
                initContentEditor() {
                    ClassicEditor
                        .create(document.querySelector('#content-editor'), {
                            language: 'zh-cn'
                        })
                        .then(editor => {
                            this.contentEditor = editor;
                        })
                        .catch(error => {
                            console.error(error);
                        });
                },
                submitForm() {
                    this.$refs.strategyForm.validate((valid) => {
                        if (valid) {
                            this.submitting = true;
                            
                            // 获取编辑器内容
                            this.form.content = this.contentEditor.getData();
                            
                            axios.post('/admin/strategy/strategy/add', this.form)
                            .then(response => {
                                this.submitting = false;
                                if (response.data.code === 1) {
                                    this.$message.success(response.data.msg);
                                    setTimeout(() => {
                                        window.location.href = '/admin/strategy/strategy/index';
                                    }, 1000);
                                } else {
                                    this.$message.error(response.data.msg);
                                }
                            })
                            .catch(error => {
                                this.submitting = false;
                                this.$message.error('提交失败');
                            });
                        }
                    });
                },
                goBack() {
                    window.location.href = '/admin/strategy/strategy/index';
                }
            }
        });
    </script>
</body>
</html>
