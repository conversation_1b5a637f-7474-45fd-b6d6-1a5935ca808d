<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>全局配置</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;全局配置</p>
        </div>

        <!--column end-->
        <div class="clas_box">
            <form action="{:url('index')}" method="post" id="myForm" enctype="multipart/form-data">
                <input type="hidden" name="id" value="{$getone.id}" />

                <div class="class_con">
                    <label>公司名称：</label>
                    <input type="text" name="company_name" value="{$getone.company_name}" />
                </div>
                <div class="class_con">
                    <label>邮箱：</label>
                    <input type="text" name="email" value="{$getone.email}" />
                </div>
                <div class="class_con">
                    <label>联系电话：</label>
                    <input type="text" name="phone" value="{$getone.phone}" />
                </div>
                <div class="class_con">
                    <label>公司地址：</label>
                    <input type="text" name="address" value="{$getone.address}" />
                </div>
                <div class="class_con">
                    <label>copyright：</label>
                    <input type="text" name="copyright" value="{$getone.copyright}" />
                </div>
                <div class="class_con editor-container">
                    <label>关于我们（标题）：</label>
                    <textarea name="about_title">{$getone.about_title}</textarea>
                </div>
                <div class="class_con editor-container">
                    <label>关于我们（描述）：</label>
                    <textarea name="about_content">{$getone.about_content}</textarea>
                </div>
                <div class="class_con">
                    <label>服务客户数量：</label>
                    <input type="text" name="customer_count" value="{$getone.customer_count}" />
                </div>
                <div class="class_con">
                    <label>案例数量：</label>
                    <input type="text" name="cases_count" value="{$getone.cases_count}" />
                </div>
                <div class="class_con">
                    <label>微信二维码：</label>
                    <input type="file" name="wx_code" />
                </div>
                <div class="class_con layout_tip_css">
                    （建议图片 宽400px 高312px。格式：png、jpg、jpeg）
                </div>
                {if condition="$getone.wx_code"}
                <div class="class_con">
                    <img src="{$getone.wx_code}">
                </div>
                {/if}

                <div class="de_y">
                    <button type="button" class="de_y_l" onclick="submitForm('myForm')">确定</button>
                </div>
            </form>
        </div>
        <!-- classify end -->
    </div>

    <span class="by">Powered by <a target="_blank" href="http://www.ggseo.cn/ezweb/">{$Think.config.app.WebConfig_VersionName}</a></span>

    {include file="common:foot"}

</body>
</html>



