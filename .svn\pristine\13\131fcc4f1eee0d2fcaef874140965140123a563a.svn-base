<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;
use PHPMailer\PHPMailer\Exception;

class Index extends Common
{
    public function index()
    {
        $this->setTdk(1);

        //服务列表
        $serviceCategory = Db::name("Service_category")->order("sort asc")->column("id, seo_url, name, icon, content");

        //客户案例
        $professions = Db::name("Cases_profession")->order("sort asc")->select();
        $professions->each(function ($item, $key) {
            $item['cases'] = Db::name('Cases')->where('profession_id', $item['id'])->order("sort asc")->limit(5)->column('id, seo_url, name, logo');
            return $item;
        });

        //博客
        //SEO Algorithm
        $algorithm = Db::name('Blog')->where('blog_type', 1)->order("publish_date desc")->limit(8)->column("id, seo_url, title, content");
        //Technical Encyclopedia
        $bkCategory = Db::name("Blog_category")->where("blog_type", 2)->order("sort asc")->limit(4)->select();
        $bkCategory->each(function ($item, $key) {
            $item['cases'] = Db::name('Blog')->where('blog_category', $item['id'])->order("publish_date desc")->column('id, seo_url, title');
            return $item;
        });
        //SEO Tools
        $tools = Db::name('Blog')->where('blog_type', 3)->field("id, seo_url, title, image, content")->limit(4)->select();

        //最新新闻
        $news = Db::name('Blog')->where('blog_type', 4)->order("publish_date desc")->field("id, seo_url, title, image, content, publish_date, publisher")->limit(6)->select();

        //问答
        $faqs = Db::name("Faqs")->order("publish_date desc")->limit(6)->select();

        return view("", [
            "serviceCategory"=> $serviceCategory,
            "professions" => $professions,
            "algorithm" => $algorithm,
            "bkCategory" => $bkCategory,
            "tools" => $tools,
            "news" => $news,
            "faqs" => $faqs,
        ]);
    }


    //测试邮件发送
    /*public function testEmail(){
        // 设置最大执行时间为300秒（5分钟）
        set_time_limit(300);

        $to = [
            '<EMAIL>',
        ];

        // 简单发送
        try {
            send_email($to, '测试邮件', '<h1>这是一封测试邮件</h1>');
            echo '发送成功';
        } catch (Exception $e) {
            echo '发送失败: ' . $e->getMessage();
        }

        // 带附件和多个收件人
        // $options = [
        //     'from_name' => '客户服务',
        //     'attachments' => [
        //         '/path/to/file1.pdf',
        //         '/path/to/file2.jpg'
        //     ]
        // ];
        // send_email($to, '会议通知', '请查收附件中的会议资料', $options);
    }*/

}
