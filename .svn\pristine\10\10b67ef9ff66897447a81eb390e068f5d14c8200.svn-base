<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>编辑案例</title>
    {include file="common/head" /}
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>编辑案例</span>
                <el-button style="float: right;" @click="goBack">返回列表</el-button>
            </div>

            <el-form ref="caseForm" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="案例标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入案例标题"></el-input>
                </el-form-item>

                <el-form-item label="SEO URL" prop="seo_url">
                    <el-input v-model="form.seo_url" placeholder="留空自动生成"></el-input>
                </el-form-item>

                <el-form-item label="关联服务" prop="service_id">
                    <el-select v-model="form.service_id" placeholder="请选择关联服务">
                        <el-option
                            v-for="service in services"
                            :key="service.id"
                            :label="service.name"
                            :value="service.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="案例图片">
                    <div class="file-upload-container image-preview">
                        <input type="file" name="image" accept="image/*">
                        <button type="button" class="upload-btn">选择图片</button>
                        <div class="file-preview">
                            <img v-if="form.image" :src="form.image" class="preview-image" style="display: block;">
                        </div>
                    </div>
                </el-form-item>

                <el-form-item label="发布日期" prop="publish_date">
                    <el-date-picker
                        v-model="form.publish_date"
                        type="date"
                        placeholder="选择发布日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>

                <el-form-item label="作者" prop="publisher">
                    <el-input v-model="form.publisher" placeholder="请输入作者"></el-input>
                </el-form-item>

                <el-form-item label="推荐">
                    <el-switch v-model="form.is_recommend"></el-switch>
                </el-form-item>

                <el-form-item label="案例内容">
                    <div class="ckeditor-textarea" data-name="content" :data-content="form.content"></div>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="submitting">提交</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    submitting: false,
                    services: <?php echo json_encode($services); ?>,
                    form: {
                        id: <?php echo $getone['id']; ?>,
                        title: '<?php echo $getone['title'] ?? ''; ?>',
                        seo_url: '<?php echo $getone['seo_url'] ?? ''; ?>',
                        service_id: <?php echo $getone['service_id'] ?? 0; ?>,
                        content: `<?php echo addslashes($getone['content'] ?? ''); ?>`,
                        image: '<?php echo $getone['image'] ?? ''; ?>',
                        publish_date: '<?php echo $getone['publish_date'] ?? ''; ?>',
                        publisher: '<?php echo $getone['publisher'] ?? ''; ?>',
                        is_recommend: <?php echo $getone['is_recommend'] ? 'true' : 'false'; ?>,
                        imagePreview: ''
                    },
                    rules: {
                        title: [
                            { required: true, message: '请输入案例标题', trigger: 'blur' }
                        ],
                        service_id: [
                            { required: true, message: '请选择关联服务', trigger: 'change' }
                        ],
                        publish_date: [
                            { required: true, message: '请选择发布日期', trigger: 'change' }
                        ],
                        publisher: [
                            { required: true, message: '请输入作者', trigger: 'blur' }
                        ]
                    }
                }
            },
            methods: {
                submitForm() {
                    this.$refs.caseForm.validate((valid) => {
                        if (valid) {
                            this.submitting = true;

                            // 使用统一的表单提交工具（自动获取编辑器内容）
                            window.submitFormUnified({
                                url: '/admin/cases/edit',
                                formData: this.form,
                                fileFields: ['image'],
                                redirectUrl: '/admin/cases/index',
                                autoGetEditorData: true, // 自动获取所有编辑器内容
                                onSuccess: (result) => {
                                    this.$message.success(result.msg || '更新成功');
                                },
                                onError: (error) => {
                                    this.$message.error(error.msg || '更新失败');
                                },
                                onComplete: () => {
                                    this.submitting = false;
                                }
                            });
                        }
                    });
                },
                goBack() {
                    window.location.href = '/admin/cases/index';
                }
            }
        });
    </script>
</body>
</html>
