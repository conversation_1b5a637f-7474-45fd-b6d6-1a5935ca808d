<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>添加FAQ</title>
    {include file="common/head" /}
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>添加FAQ</span>
                <el-button style="float: right;" @click="goBack">返回列表</el-button>
            </div>

            <el-form ref="faqForm" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="FAQ标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入FAQ标题"></el-input>
                </el-form-item>

                <el-form-item label="SEO URL" prop="seo_url">
                    <el-input v-model="form.seo_url" placeholder="留空自动生成"></el-input>
                </el-form-item>

                <el-form-item label="关联服务" prop="service_id">
                    <el-select v-model="form.service_id" placeholder="请选择关联服务">
                        <el-option
                            v-for="service in services"
                            :key="service.id"
                            :label="service.name"
                            :value="service.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="发布日期" prop="publish_date">
                    <el-date-picker
                        v-model="form.publish_date"
                        type="date"
                        placeholder="选择发布日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>

                <el-form-item label="作者" prop="publisher">
                    <el-input v-model="form.publisher" placeholder="请输入作者"></el-input>
                </el-form-item>

                <el-form-item label="推荐">
                    <el-switch v-model="form.is_recommend"></el-switch>
                </el-form-item>

                <el-form-item label="FAQ内容">
                    <div class="ckeditor-textarea" data-name="content"></div>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="submitting">提交</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    submitting: false,
                    services: <?php echo json_encode($services); ?>,
                    form: {
                        title: '',
                        seo_url: '',
                        service_id: '',
                        content: '',
                        publish_date: '',
                        publisher: '',
                        is_recommend: false
                    },
                    rules: {
                        title: [
                            { required: true, message: '请输入FAQ标题', trigger: 'blur' }
                        ],
                        service_id: [
                            { required: true, message: '请选择关联服务', trigger: 'change' }
                        ],
                        publish_date: [
                            { required: true, message: '请选择发布日期', trigger: 'change' }
                        ],
                        publisher: [
                            { required: true, message: '请输入作者', trigger: 'blur' }
                        ]
                    }
                }
            },
            mounted() {
                // 设置默认发布日期为今天
                this.form.publish_date = new Date().toISOString().split('T')[0];
            },
            methods: {
                submitForm() {
                    window.AdminUtils.submitForm(this, {
                        formRef: 'faqForm',
                        url: "{:url('add')}",
                        formData: this.form,
                        fileFields: [],
                        redirectUrl: "{:url('index')}",
                        successMessage: '添加成功',
                        errorMessage: '添加失败'
                    });
                },
                goBack() {
                    window.AdminUtils.goBack("{:url('index')}");
                }
            }
        });
    </script>
</body>
</html>
