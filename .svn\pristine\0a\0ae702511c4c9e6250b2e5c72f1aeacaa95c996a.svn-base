<link type="text/css" rel="stylesheet" href="__CSS__/index.css"/>
<link type="text/css" rel="stylesheet" href="__CSS__/public.css"/>

<!-- tiny编辑器 -->
<script src="https://cdn.tiny.cloud/1/6l7sp77p6m8qlg5eyeh2je3ubl8azrt5dd4xxxc6ddwe7g8f/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/langs/zh_CN.js" referrerpolicy="origin"></script>
<script>
    // 通用配置
    const defaultTinyMCEConfig = {
        language: 'zh_CN',
        plugins: 'autoresize code image media link',
        height: '250px',
        toolbar: 'code | customMediaUpload image media | undo redo | bold italic | alignleft aligncenter alignright alignjustify | fontsize | link',

        file_picker_types: 'media image', // 允许选择媒体和图片文件
        images_upload_url: '/admin/tinymceImage', // 图片上传接口

        min_height: 200,      // 最小高度200px
        max_height: 600,      // 最大高度600px
        autoresize_bottom_margin: 10, // 底部留白
        autoresize_on_init: true ,     // 初始化时自动调整

        setup: function(editor) {
            editor.ui.registry.addButton('customMediaUpload', {
                icon: 'embed',
                tooltip: '插入视频',
                onAction: function() {
                    editor.windowManager.open({
                        title: '上传视频',
                        body: {
                            type: 'panel',
                                items: [{
                                type: 'htmlpanel',
                                html: '<input type="file" id="tinymce-media-upload" accept="video/*,audio/*">'
                            }]
                        },
                        buttons: [
                            {
                                type: 'cancel',
                                name: 'cancel',
                                text: 'Cancel'
                            },
                            {
                                type: 'submit',
                                name: 'save',
                                text: 'Upload',
                                primary: true
                            }
                        ],
                        onSubmit: function(api) {
                            var fileInput = document.getElementById('tinymce-media-upload');
                            if (fileInput.files.length > 0) {
                                var formData = new FormData();
                                formData.append('file', fileInput.files[0]);

                                fetch('/admin/tinymceMedia', {
                                    method: 'POST',
                                    body: formData
                                })
                                .then(response => response.json())
                                .then(data => {
                                    console.log(data)
                                    if (data.location) {
                                        editor.insertContent(
                                            data.location.match(/\.(mp4|webm|ogg|mov|avi)$/) ?
                                            '<video controls src="' + data.location + '"></video>' :
                                            '<audio controls src="' + data.location + '"></audio>'
                                        );
                                        api.close();
                                    }
                                });
                            }
                        }
                    });
                }
            });
        }
    };

    // 封装初始化函数
    function initTinyMCE(selector, customConfig = {}) {
        tinymce.init({
            selector: selector,
            ...defaultTinyMCEConfig, // 通用配置
            ...customConfig, // 自定义配置
        });
    }
</script>