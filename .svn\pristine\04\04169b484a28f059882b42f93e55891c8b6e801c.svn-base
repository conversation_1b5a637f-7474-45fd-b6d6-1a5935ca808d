{"name": "wenh<PERSON>an/thinkphp6-auth", "description": "auth package for thinkphp6", "homepage": "https://github.com/wenhainan/thinkphp6-auth", "license": "Apache-2.0", "minimum-stability": "stable", "version": "1.1.2", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/wenhainan/thinkphp6-auth/issues"}, "require": {"php": ">=5.4.0"}, "autoload": {"psr-4": {"think\\wenhainan\\": "src/"}}, "extra": {"think": {"config": {"auth": "src/config/auth.php"}}}}