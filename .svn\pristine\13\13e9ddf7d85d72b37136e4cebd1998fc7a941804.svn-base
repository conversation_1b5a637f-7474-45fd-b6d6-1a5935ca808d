<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;

class Index extends Common
{
    public function index()
    {
        $this->setTdk(1);

        $optimization_direction = Db::name('optimization_direction')
            ->order("sort asc, id desc")
            ->select();

        $values = Db::name('values')
            ->order("sort asc, id desc")
            ->select();

        $service = Db::name('Service')
            ->order("sort asc, id desc")
            ->select();

        $cases = Db::name('cases')
            ->where("is_recommend", 1)
            ->order("publish_date desc, id desc")
            ->limit(6)
            ->select();

        $faq = Db::name('faq')
            ->where("is_recommend", 1)
            ->order("publish_date desc, id desc")
            ->limit(6)
            ->select();

        return view("", [
            "optimization_direction" => $optimization_direction,
            "values" => $values,
            "service" => $service,
            "cases" => $cases,
            "faq" => $faq,
        ]);
    }



}
