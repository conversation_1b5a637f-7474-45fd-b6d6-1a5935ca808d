<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>服务管理</title>
    {include file="common/head" /}
    <style>
        .search-form { margin-bottom: 20px; }
        .el-table { margin-top: 20px; }
        .service-image { width: 60px; height: 40px; object-fit: cover; border-radius: 4px; }
        .service-icon { width: 30px; height: 30px; object-fit: cover; border-radius: 4px; }
    </style>
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>服务管理</span>
                <el-button style="float: right;" type="primary" @click="addService">添加服务</el-button>
            </div>

            <!-- 搜索表单 -->
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="关键词">
                    <el-input v-model="searchForm.keyword" placeholder="请输入服务名称或标题" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>

            <!-- 服务列表 -->
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column label="图标" width="80">
                    <template slot-scope="scope">
                        <img v-if="scope.row.icon" :src="scope.row.icon" class="service-icon" />
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="服务名称" width="150"></el-table-column>
                <el-table-column prop="title" label="服务标题" width="200"></el-table-column>
                <el-table-column label="服务图片" width="100">
                    <template slot-scope="scope">
                        <img v-if="scope.row.image" :src="scope.row.image" class="service-image" />
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="seo_url" label="SEO URL" width="150"></el-table-column>
                <el-table-column prop="sort" label="排序" width="80"></el-table-column>
                <el-table-column label="推荐" width="80">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.is_recommend ? 'success' : 'info'" size="small">
                            {{ scope.row.is_recommend ? '是' : '否' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="editService(scope.row.id)">编辑</el-button>
                        <el-button size="mini" type="danger" @click="deleteService(scope.row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="20"
                layout="total, prev, pager, next, jumper"
                :total="total"
                style="margin-top: 20px; text-align: center;">
            </el-pagination>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    tableData: <?php echo json_encode($List->items()); ?>,
                    currentPage: <?php echo $List->currentPage(); ?>,
                    total: <?php echo $List->total(); ?>,
                    searchForm: {
                        keyword: '<?php echo $keyword ?? ""; ?>'
                    }
                }
            },
            methods: {
                search() {
                    const params = new URLSearchParams();
                    if (this.searchForm.keyword) {
                        params.append('keyword', this.searchForm.keyword);
                    }
                    window.location.href = '/admin/service/index?' + params.toString();
                },
                resetSearch() {
                    this.searchForm.keyword = '';
                    window.location.href = '/admin/service/index';
                },
                handleCurrentChange(page) {
                    const params = new URLSearchParams(window.location.search);
                    params.set('page', page);
                    window.location.href = '/admin/service/index?' + params.toString();
                },
                addService() {
                    window.location.href = '/admin/service/add';
                },
                editService(id) {
                    window.location.href = '/admin/service/edit?id=' + id;
                },
                deleteService(id) {
                    this.$confirm('确定要删除这个服务吗？删除后将同时删除相关的策略、优势和流程数据。', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.post('/admin/service/del', { id: id })
                            .then(response => {
                                if (response.data.code === 1) {
                                    this.$message.success(response.data.msg);
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 1000);
                                } else {
                                    this.$message.error(response.data.msg);
                                }
                            })
                            .catch(error => {
                                this.$message.error('删除失败');
                            });
                    });
                }
            }
        });
    </script>
</body>
</html>
