<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>编辑价值</title>
    {include file="common/head" /}
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>编辑价值</span>
                <el-button style="float: right;" @click="goBack">返回列表</el-button>
            </div>

            <el-form ref="valuesForm" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入标题"></el-input>
                </el-form-item>

                <el-form-item label="排序">
                    <el-input-number v-model="form.sort" :min="0" :max="999"></el-input-number>
                </el-form-item>

                <el-form-item label="内容">
                    <div class="ckeditor-textarea" data-name="content" :data-content="form.content"></div>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="submitting">提交</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    submitting: false,
                    form: {
                        id: <?php echo $getone['id']; ?>,
                        title: '<?php echo $getone['title'] ?? ''; ?>',
                        content: `<?php echo addslashes($getone['content'] ?? ''); ?>`,
                        sort: <?php echo $getone['sort'] ?? 50; ?>
                    },
                    rules: {
                        title: [
                            { required: true, message: '请输入标题', trigger: 'blur' }
                        ]
                    }
                }
            },
            methods: {
                submitForm() {
                    this.$refs.valuesForm.validate((valid) => {
                        if (valid) {
                            this.submitting = true;

                            // 使用统一的表单提交工具（自动获取编辑器内容）
                            window.submitFormUnified({
                                url: "{:url('valuesEdit')}",
                                formData: this.form,
                                fileFields: [],
                                redirectUrl: "{:url('valuesIndex')}",
                                autoGetEditorData: true, // 自动获取所有编辑器内容
                                successMessage: '更新成功',
                                errorMessage: '更新失败'
                            });
                        }
                    });
                },
                goBack() {
                    window.location.href = "{:url('valuesIndex')}";
                }
            }
        });
    </script>
</body>
</html>
