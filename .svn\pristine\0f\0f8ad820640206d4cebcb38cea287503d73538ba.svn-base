<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>添加优化方向</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <style>
        .optimization-container { padding: 20px; }
        .ck-editor__editable { min-height: 200px; }
        .upload-demo { display: flex; align-items: center; gap: 10px; }
        .image-preview { width: 60px; height: 60px; object-fit: cover; border-radius: 4px; }
    </style>
</head>
<body>
    <div id="app" class="optimization-container">
        <el-card>
            <div slot="header">
                <span>添加优化方向</span>
                <el-button style="float: right;" @click="goBack">返回列表</el-button>
            </div>

            <el-form ref="optimizationForm" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入标题"></el-input>
                </el-form-item>

                <el-form-item label="图标">
                    <div class="upload-demo">
                        <input
                            type="file"
                            name="icon"
                            accept="image/*"
                            @change="handleIconChange"
                            ref="iconInput"
                            style="display: none;">
                        <el-button size="small" type="primary" @click="$refs.iconInput.click()">选择图标</el-button>
                        <img v-if="form.iconPreview" :src="form.iconPreview" class="image-preview" />
                    </div>
                </el-form-item>

                <el-form-item label="排序">
                    <el-input-number v-model="form.sort" :min="0" :max="999"></el-input-number>
                </el-form-item>

                <el-form-item label="内容">
                    <div id="content-editor"></div>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="submitting">提交</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    submitting: false,
                    contentEditor: null,
                    form: {
                        title: '',
                        content: '',
                        sort: 50,
                        iconPreview: ''
                    },
                    rules: {
                        title: [
                            { required: true, message: '请输入标题', trigger: 'blur' }
                        ]
                    }
                }
            },
            mounted() {
                this.initContentEditor();
            },
            methods: {
                initContentEditor() {
                    ClassicEditor
                        .create(document.querySelector('#content-editor'), {
                            language: 'zh-cn'
                        })
                        .then(editor => {
                            this.contentEditor = editor;
                        })
                        .catch(error => {
                            console.error(error);
                        });
                },
                handleIconChange(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.form.iconFile = file;
                        this.form.iconPreview = URL.createObjectURL(file);
                    }
                },
                submitForm() {
                    this.$refs.optimizationForm.validate((valid) => {
                        if (valid) {
                            this.submitting = true;

                            // 获取编辑器内容
                            this.form.content = this.contentEditor.getData();

                            // 创建FormData对象
                            const formData = new FormData();

                            // 添加基本字段
                            Object.keys(this.form).forEach(key => {
                                if (!['iconFile', 'iconPreview'].includes(key)) {
                                    formData.append(key, this.form[key]);
                                }
                            });

                            // 添加文件
                            if (this.form.iconFile) {
                                formData.append('icon', this.form.iconFile);
                            }

                            axios.post('/admin/homepage/optimization/add', formData, {
                                headers: {
                                    'Content-Type': 'multipart/form-data'
                                }
                            })
                            .then(response => {
                                this.submitting = false;
                                console.log(response)
                                if (response.data.code === 1) {
                                    this.$message.success(response.data.msg);
                                    setTimeout(() => {
                                        window.location.href = '/admin/homepage/optimization/index';
                                    }, 1000);
                                } else {
                                    this.$message.error(response.data.msg);
                                }
                            })
                            .catch(error => {
                                this.submitting = false;
                                this.$message.error('提交失败');
                            });
                        }
                    });
                },
                goBack() {
                    window.location.href = '/admin/homepage/optimization/index';
                }
            }
        });
    </script>
</body>
</html>
