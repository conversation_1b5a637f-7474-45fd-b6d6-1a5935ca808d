<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>优化方向管理</title>
    {include file="common/head" /}
    <style>
        .search-form { margin-bottom: 20px; }
        .el-table { margin-top: 20px; }
        .optimization-icon { width: 30px; height: 30px; object-fit: cover; border-radius: 4px; }
        .content-preview { max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
    </style>
</head>
<body>
    <div id="app" class="optimization-container">
        <el-card>
            <div slot="header">
                <span>优化方向管理</span>
                <el-button style="float: right;" type="primary" @click="addOptimization">添加优化方向</el-button>
            </div>

            <!-- 搜索表单 -->
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="关键词">
                    <el-input v-model="searchForm.keyword" placeholder="请输入标题" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>

            <!-- 优化方向列表 -->
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column label="图标" width="80">
                    <template slot-scope="scope">
                        <img v-if="scope.row.icon" :src="scope.row.icon" class="optimization-icon" />
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="title" label="标题" width="200"></el-table-column>
                <el-table-column label="内容" width="300">
                    <template slot-scope="scope">
                        <div class="content-preview">{{ getContentPreview(scope.row.content) }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="sort" label="排序" width="80"></el-table-column>
                <el-table-column label="操作" width="200">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="editOptimization(scope.row.id)">编辑</el-button>
                        <el-button size="mini" type="danger" @click="deleteOptimization(scope.row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="20"
                layout="total, prev, pager, next, jumper"
                :total="total"
                style="margin-top: 20px; text-align: center;">
            </el-pagination>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    tableData: <?php echo json_encode($List->items()); ?>,
                    currentPage: <?php echo $List->currentPage(); ?>,
                    total: <?php echo $List->total(); ?>,
                    searchForm: {
                        keyword: '<?php echo $keyword ?? ""; ?>'
                    }
                }
            },
            methods: {
                // 获取内容预览（去除HTML标签，限制字符数）
                getContentPreview(content) {
                    if (!content) return '-';

                    // 去除HTML标签
                    const textContent = content.replace(/<[^>]*>/g, '');

                    // 限制显示字符数
                    const maxLength = 50;
                    if (textContent.length > maxLength) {
                        return textContent.substring(0, maxLength) + '...';
                    }

                    return textContent || '-';
                },

                search() {
                    const params = new URLSearchParams();
                    if (this.searchForm.keyword) {
                        params.append('keyword', this.searchForm.keyword);
                    }
                    window.location.href = "{:url('optimizationIndex')}?" + params.toString();
                },
                resetSearch() {
                    this.searchForm.keyword = '';
                    window.location.href = "{:url('optimizationIndex')}";
                },
                handleCurrentChange(page) {
                    const params = new URLSearchParams(window.location.search);
                    params.set('page', page);
                    window.location.href = "{:url('optimizationIndex')}?" + params.toString();
                },
                addOptimization() {
                    window.location.href = "{:url('optimizationAdd')}";
                },
                editOptimization(id) {
                    window.location.href = "{:url('optimizationEdit')}?id=" + id;
                },
                deleteOptimization(id) {
                    this.$confirm('确定要删除这个优化方向吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.post("{:url('optimizationDel')}", { id: id })
                            .then(response => {
                                if (response.data.code === 1) {
                                    this.$message.success(response.data.msg);
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 1000);
                                } else {
                                    this.$message.error(response.data.msg);
                                }
                            })
                            .catch(error => {
                                this.$message.error('删除失败');
                            });
                    });
                }
            }
        });
    </script>
</body>
</html>
