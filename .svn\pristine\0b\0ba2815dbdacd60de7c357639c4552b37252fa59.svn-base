<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;

class Faq extends Common
{
    public function index()
    {
        $this->setTdk(5);

        $faqs = Db::name("Faq")
            ->order("publish_date desc")
            ->paginate([
                'list_rows' => 10,
                'var_page' => 'page',
            ]);

        return view("", [
            "faqs"=> $faqs,
        ]);
    }

    public function details()
    {
        $seo_url = input("url");

        $getone = Db::name("Faq")->where("seo_url", $seo_url)->find();
        if (empty($getone)) {
            return view("error/404")->code(404);
        }

        $tdk = [
            "seo_title"=> $getone["seo_title"]?$getone["seo_title"]:strip_tags($getone["title"]),
            "seo_description"=> $getone["seo_description"],
            "seo_keywords"=> $getone["seo_keywords"],
        ];

        //推荐案例
        $cases = Db::name('Cases')->where("is_recommend", 1)->order("publish_date desc")->limit(3)->select();

        // 获取上一篇FAQ（发布时间更早的）
        $prevCase = Db::name('Faq')
            ->where('publish_date', '<', $getone['publish_date'])
            ->whereOr(function($query) use ($getone) {
                $query->where('publish_date', '=', $getone['publish_date'])
                      ->where('id', '<', $getone['id']);
            })
            ->order('publish_date desc, id desc')
            ->field('id, title, seo_url')
            ->find();

        // 获取下一篇FAQ（发布时间更晚的）
        $nextCase = Db::name('Faq')
            ->where('publish_date', '>', $getone['publish_date'])
            ->whereOr(function($query) use ($getone) {
                $query->where('publish_date', '=', $getone['publish_date'])
                      ->where('id', '>', $getone['id']);
            })
            ->order('publish_date asc, id asc')
            ->field('id, title, seo_url')
            ->find();

        return view("", [
            "getone"=> $getone,
            "tdk" => $tdk,
            "cases" => $cases,
            "prevCase" => $prevCase,
            "nextCase" => $nextCase,
        ]);
    }

}
