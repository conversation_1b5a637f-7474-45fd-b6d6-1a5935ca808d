<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;

class Cases extends Common
{
    public function index()
    {
        $this->setTdk(3);

        //服务类别
        $serviceCategory = Db::name("Service_category")->order("sort asc")->column("id, name");

        //行业
        $professions = Db::name("Cases_profession")->order("sort asc")->column("id, name");

        // 获取筛选条件（使用简洁参数名）
        $service = input('service', 0);
        $profession = input('profession', 0);

        // 构建查询条件
        $where = [];
        if ($service) {
            $where['service_category'] = $service;
        }
        if ($profession) {
            $where['profession_id'] = $profession;
        }
        // 查询数据并分页
        $cases = Db::name('Cases')
        ->where($where)
        ->order('publish_date', 'desc')
        ->paginate([
            'list_rows' => 6,
            'var_page' => 'page',
            'query' => [
                'service' => $service,
                'profession' => $profession
            ]
        ])
        ->each(function ($item, $key) {
            $item['service_category'] = Db::name('Service_category')->where('id', $item['service_category'])->field("id, name, color")->find();
            return $item;
        });

        //FAQ
        $faqs = Db::name("Faqs")->select();

        return view("", [
            "serviceCategory"=> $serviceCategory,
            "professions" => $professions,
            "cases" => $cases,
            "faqs" => $faqs,
        ]);
    }

    public function details()
    {
        $seo_url = input("url");

        $getone = Db::name("Cases")->where("seo_url", $seo_url)->find();

        $tdk = [
            "seo_title"=> $getone["seo_title"]?$getone["seo_title"]:strip_tags($getone["title"]),
            "seo_description"=> $getone["seo_description"],
            "seo_keywords"=> $getone["seo_keywords"],
        ];

        // 获取下一篇文章（发布时间更近的）
        $next = Db::name('Cases')
            ->where('publish_date', '>', $getone['publish_date'])
            ->order('publish_date', 'asc') // 正序找比当前大的最小一个
            ->field('id, seo_url, title')
            ->find();
        // 如果没有更晚的文章，就找发布时间相同的其他文章
        if (!$next) {
            $next = Db::name('Cases')
                ->where('publish_date', '=', $getone['publish_date'])
                ->where('id', '<', $getone['id'])
                ->order('id', 'desc')
                ->field('id, seo_url, title')
                ->find();
        }

        // 获取上一篇文章（发布时间更早的）
        $prev = Db::name('Cases')
            ->where('publish_date', '<', $getone['publish_date'])
            ->order('publish_date', 'desc') // 倒序找比当前小的最大一个
            ->field('id, seo_url, title')
            ->find();
        // 如果没有更早的文章，就找发布时间相同的其他文章
        if (!$prev) {
            $prev = Db::name('Cases')
                ->where('publish_date', '=', $getone['publish_date'])
                ->where('id', '>', $getone['id'])
                ->order('id', 'asc')
                ->field('id, seo_url, title')
                ->find();
        }

        //相关FAQ。案例和FAQ有间接相关性，案例相关FAQ=（案例所属服务分类）的相关FAQ
        $faqs = Db::name("Faqs")->where("service_category", $getone['service_category'])->order("publish_date desc")->select();

        return view("", [
            "getone"=> $getone,
            "tdk"=> $tdk,
            "prev" => $prev,
            "next" => $next,
            "faqs"=> $faqs,
        ]);
    }

}
