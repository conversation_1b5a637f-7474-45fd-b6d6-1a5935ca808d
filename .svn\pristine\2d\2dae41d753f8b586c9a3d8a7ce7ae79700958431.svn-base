<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>策略流程管理</title>
    {include file="common/head" /}
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>策略流程管理</span>
                <el-button style="float: right;" type="primary" @click="addProcess">添加流程</el-button>
            </div>

            <!-- 搜索表单 -->
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="关键词">
                    <el-input v-model="searchForm.keyword" placeholder="请输入流程标题" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>

            <!-- 流程列表 -->
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column prop="title" label="流程标题" width="250"></el-table-column>
                <el-table-column label="内容" width="400">
                    <template slot-scope="scope">
                        <div class="content-preview" v-html="scope.row.content"></div>
                    </template>
                </el-table-column>
                <el-table-column prop="sort" label="排序" width="80"></el-table-column>
                <el-table-column label="操作" width="200">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="editProcess(scope.row.id)">编辑</el-button>
                        <el-button size="mini" type="danger" @click="deleteProcess(scope.row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="20"
                layout="total, prev, pager, next, jumper"
                :total="total"
                style="margin-top: 20px; text-align: center;">
            </el-pagination>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    tableData: <?php echo json_encode($List->items()); ?>,
                    currentPage: <?php echo $List->currentPage(); ?>,
                    total: <?php echo $List->total(); ?>,
                    searchForm: {
                        keyword: '<?php echo $keyword ?? ""; ?>'
                    }
                }
            },
            methods: {
                search() {
                    const params = new URLSearchParams();
                    if (this.searchForm.keyword) {
                        params.append('keyword', this.searchForm.keyword);
                    }
                    window.location.href = '/admin/strategy/process/index?' + params.toString();
                },
                resetSearch() {
                    this.searchForm.keyword = '';
                    window.location.href = '/admin/strategy/process/index';
                },
                handleCurrentChange(page) {
                    const params = new URLSearchParams(window.location.search);
                    params.set('page', page);
                    window.location.href = '/admin/strategy/process/index?' + params.toString();
                },
                addProcess() {
                    window.location.href = '/admin/strategy/process/add';
                },
                editProcess(id) {
                    window.location.href = '/admin/strategy/process/edit?id=' + id;
                },
                deleteProcess(id) {
                    this.$confirm('确定要删除这个流程吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.post('/admin/strategy/process/del', { id: id })
                            .then(response => {
                                if (response.data.code === 1) {
                                    this.$message.success(response.data.msg);
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 1000);
                                } else {
                                    this.$message.error(response.data.msg);
                                }
                            })
                            .catch(error => {
                                this.$message.error('删除失败');
                            });
                    });
                }
            }
        });
    </script>
</body>
</html>
