<?php
namespace app\admin\route;

use think\facade\Route;

// 登录相关
Route::get('login', 'Login/index');
Route::post('login', 'Login/index');
Route::get('logout', 'Login/logout');
Route::get('captcha', 'Login/captcha');

// 后台首页
Route::get('/', 'Index/index');

// 管理员管理
Route::group('admin', function () {
    Route::get('index', 'Admin/index');
    Route::get('add', 'Admin/add');
    Route::post('add', 'Admin/add');
    Route::get('edit', 'Admin/edit');
    Route::post('edit', 'Admin/edit');
    Route::post('delete', 'Admin/delete');
    Route::get('myeditpwd', 'Admin/myeditpwd');
    Route::post('myeditpwd', 'Admin/myeditpwd');
});

// 角色管理
Route::group('role', function () {
    Route::get('index', 'Role/index');
    Route::get('add', 'Role/add');
    Route::post('add', 'Role/add');
    Route::get('edit', 'Role/edit');
    Route::post('edit', 'Role/edit');
    Route::post('delete', 'Role/delete');
});

// 服务管理
Route::group('service', function () {
    Route::get('index', 'Service/index');
    Route::get('add', 'Service/add');
    Route::post('add', 'Service/add');
    Route::get('edit', 'Service/edit');
    Route::post('edit', 'Service/edit');
    Route::post('del', 'Service/del');
});

// 首页管理
Route::group('homepage', function () {
    // 优化方向
    Route::get('optimization/index', 'Homepage/optimizationIndex');
    Route::get('optimization/add', 'Homepage/optimizationAdd');
    Route::post('optimization/add', 'Homepage/optimizationAdd');
    Route::get('optimization/edit', 'Homepage/optimizationEdit');
    Route::post('optimization/edit', 'Homepage/optimizationEdit');
    Route::post('optimization/del', 'Homepage/optimizationDel');

    // 本质区别
    Route::get('difference/index', 'Homepage/differenceIndex');
    Route::get('difference/add', 'Homepage/differenceAdd');
    Route::post('difference/add', 'Homepage/differenceAdd');
    Route::get('difference/edit', 'Homepage/differenceEdit');
    Route::post('difference/edit', 'Homepage/differenceEdit');
    Route::post('difference/del', 'Homepage/differenceDel');

    // 价值列表
    Route::get('values/index', 'Homepage/valuesIndex');
    Route::get('values/add', 'Homepage/valuesAdd');
    Route::post('values/add', 'Homepage/valuesAdd');
    Route::get('values/edit', 'Homepage/valuesEdit');
    Route::post('values/edit', 'Homepage/valuesEdit');
    Route::post('values/del', 'Homepage/valuesDel');
});

// 案例管理
Route::group('cases', function () {
    Route::get('index', 'Cases/index');
    Route::get('add', 'Cases/add');
    Route::post('add', 'Cases/add');
    Route::get('edit', 'Cases/edit');
    Route::post('edit', 'Cases/edit');
    Route::post('del', 'Cases/del');
});

// FAQ管理
Route::group('faq', function () {
    Route::get('index', 'Faq/index');
    Route::get('add', 'Faq/add');
    Route::post('add', 'Faq/add');
    Route::get('edit', 'Faq/edit');
    Route::post('edit', 'Faq/edit');
    Route::post('del', 'Faq/del');
});

// 主要产品管理
Route::group('product', function () {
    Route::get('index', 'Product/index');
    Route::get('add', 'Product/add');
    Route::post('add', 'Product/add');
    Route::get('edit', 'Product/edit');
    Route::post('edit', 'Product/edit');
    Route::post('del', 'Product/del');
});

// 策略管理
Route::group('strategy', function () {
    // 策略列表
    Route::get('strategy/index', 'Strategy/strategyIndex');
    Route::get('strategy/add', 'Strategy/strategyAdd');
    Route::post('strategy/add', 'Strategy/strategyAdd');
    Route::get('strategy/edit', 'Strategy/strategyEdit');
    Route::post('strategy/edit', 'Strategy/strategyEdit');
    Route::post('strategy/del', 'Strategy/strategyDel');

    // 优势列表
    Route::get('advantage/index', 'Strategy/advantageIndex');
    Route::get('advantage/add', 'Strategy/advantageAdd');
    Route::post('advantage/add', 'Strategy/advantageAdd');
    Route::get('advantage/edit', 'Strategy/advantageEdit');
    Route::post('advantage/edit', 'Strategy/advantageEdit');
    Route::post('advantage/del', 'Strategy/advantageDel');

    // 流程列表
    Route::get('process/index', 'Strategy/processIndex');
    Route::get('process/add', 'Strategy/processAdd');
    Route::post('process/add', 'Strategy/processAdd');
    Route::get('process/edit', 'Strategy/processEdit');
    Route::post('process/edit', 'Strategy/processEdit');
    Route::post('process/del', 'Strategy/processDel');
});

// 基本设置
Route::group('basic', function () {
    Route::get('index', 'Basic/index');
    Route::post('index', 'Basic/index');
});

// SEO设置
Route::group('seo', function () {
    Route::get('index', 'Seo/index');
    Route::get('add', 'Seo/add');
    Route::post('add', 'Seo/add');
    Route::get('edit', 'Seo/edit');
    Route::post('edit', 'Seo/edit');
    Route::post('del', 'Seo/del');
});

// 通用上传
Route::post('upload', 'Common/upload');
?>
