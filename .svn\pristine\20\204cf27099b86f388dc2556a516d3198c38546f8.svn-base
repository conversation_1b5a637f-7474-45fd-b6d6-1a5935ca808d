<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>本质区别管理</title>
    {include file="common/head" /}
    <style>
        .search-form { margin-bottom: 20px; }
        .el-table { margin-top: 20px; }
        .difference-icon { width: 30px; height: 30px; object-fit: cover; border-radius: 4px; }
        .content-preview { max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
    </style>
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>本质区别管理</span>
                <el-button style="float: right;" type="primary" @click="addDifference">添加本质区别</el-button>
            </div>

            <!-- 搜索表单 -->
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="关键词">
                    <el-input v-model="searchForm.keyword" placeholder="请输入名称" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>

            <!-- 本质区别列表 -->
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column label="图标" width="80">
                    <template slot-scope="scope">
                        <img v-if="scope.row.icon" :src="scope.row.icon" class="difference-icon" />
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="名称" width="150"></el-table-column>
                <el-table-column label="GEO内容" width="200">
                    <template slot-scope="scope">
                        <div class="content-preview">{{ getContentPreview(scope.row.geo_content) }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="SEO内容" width="200">
                    <template slot-scope="scope">
                        <div class="content-preview">{{ getContentPreview(scope.row.seo_content) }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="sort" label="排序" width="80"></el-table-column>
                <el-table-column label="操作" width="200">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="editDifference(scope.row.id)">编辑</el-button>
                        <el-button size="mini" type="danger" @click="deleteDifference(scope.row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="20"
                layout="total, prev, pager, next, jumper"
                :total="total"
                style="margin-top: 20px; text-align: center;">
            </el-pagination>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    tableData: <?php echo json_encode($List->items()); ?>,
                    currentPage: <?php echo $List->currentPage(); ?>,
                    total: <?php echo $List->total(); ?>,
                    searchForm: {
                        keyword: '<?php echo $keyword ?? ""; ?>'
                    }
                }
            },
            methods: {
                // 获取内容预览（去除HTML标签，限制字符数）
                getContentPreview(content) {
                    if (!content) return '-';

                    // 去除HTML标签
                    const textContent = content.replace(/<[^>]*>/g, '');

                    // 限制显示字符数
                    const maxLength = 30;
                    if (textContent.length > maxLength) {
                        return textContent.substring(0, maxLength) + '...';
                    }

                    return textContent || '-';
                },

                search() {
                    const params = new URLSearchParams();
                    if (this.searchForm.keyword) {
                        params.append('keyword', this.searchForm.keyword);
                    }
                    window.location.href = "{:url('differenceIndex')}?" + params.toString();
                },
                resetSearch() {
                    this.searchForm.keyword = '';
                    window.location.href = "{:url('differenceIndex')}";
                },
                handleCurrentChange(page) {
                    const params = new URLSearchParams(window.location.search);
                    params.set('page', page);
                    window.location.href = "{:url('differenceIndex')}?" + params.toString();
                },
                addDifference() {
                    window.location.href = "{:url('differenceAdd')}";
                },
                editDifference(id) {
                    window.location.href = "{:url('differenceEdit')}?id=" + id;
                },
                deleteDifference(id) {
                    this.$confirm('确定要删除这个本质区别吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.post("{:url('differenceDel')}", { id: id })
                            .then(response => {
                                if (response.data.code === 1) {
                                    this.$message.success(response.data.msg);
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 1000);
                                } else {
                                    this.$message.error(response.data.msg);
                                }
                            })
                            .catch(error => {
                                this.$message.error('删除失败');
                            });
                    });
                }
            }
        });
    </script>
</body>
</html>
