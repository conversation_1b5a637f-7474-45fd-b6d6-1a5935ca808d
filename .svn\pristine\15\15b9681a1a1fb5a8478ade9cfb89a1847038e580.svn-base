<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\admin\BaseController;
use think\facade\Session;
use think\facade\Filesystem;
use think\exception\ValidateException;
use think\file\UploadedFile;
use think\facade\View;

class Common extends BaseController
{
    protected $adminInfo = [];

    public function initialize()
    {
        // 检查登录状态
        $adminId = Session::get('admin_id');
        if (!$adminId) {
            if (request()->isAjax()) {
                return json(['code' => -1, 'msg' => '请先登录', 'url' => '/admin/login']);
            } else {
                $this->redirect('/admin/login');
            }
        }

        // 获取管理员信息
        $this->adminInfo = [
            'id' => Session::get('admin_id'),
            'username' => Session::get('admin_username'),
            'nickname' => Session::get('admin_nickname'),
            'role' => Session::get('admin_role'),
            'permissions' => Session::get('admin_permissions', [])
        ];

        // 将管理员信息传递给视图
        View::assign("adminInfo", $this->adminInfo);
    }

    // 检查权限
    protected function checkPermission($permission)
    {
        $permissions = $this->adminInfo['permissions'];

        // 超级管理员拥有所有权限
        if (in_array('*', $permissions)) {
            return true;
        }

        // 检查具体权限
        return in_array($permission, $permissions);
    }

    // 文件上传
    protected function upload(UploadedFile $file, $path = '')
    {
        try {
            // 验证文件
            validate(['file' => [
                'fileSize' => 1024 * 1024 * 5, // 5MB
                'fileExt'  => 'jpg,jpeg,png,gif,webp'
            ]])->check(['file' => $file]);

            $savename = Filesystem::disk('public')->putFile($path, $file);
            $url = Filesystem::getDiskConfig('public', 'url') . '/' . str_replace('\\', '/', $savename);

            return $url;
        } catch (ValidateException $e) {
            $this->error($e->getMessage());
        }
    }


    public function editorImage()
    {
        try {
            $file = request()->file('upload');

            // 验证文件
            validate(['file' => [
                'fileSize' => 1024 * 1024 * 5, // 5MB
                'fileExt'  => 'jpg,jpeg,png,gif,webp'
            ]])->check(['file' => $file]);

            $savename = Filesystem::disk('public')->putFile('editor/images', $file);
            $url = Filesystem::getDiskConfig('public', 'url') . '/' . str_replace('\\', '/', $savename);

            return json([
                'uploaded' => true,
                'url' => $url
            ]);
        } catch (\Exception $e) {
            return json([
                'uploaded' => false,
                'error' => [
                    'message' => $e->getMessage()
                ]
            ]);
        }
    }
}
