<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>案例管理</title>
    {include file="common/head" /}
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>案例管理</span>
                <el-button style="float: right;" type="primary" @click="addCase">添加案例</el-button>
            </div>

            <!-- 搜索表单 -->
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="关键词">
                    <el-input v-model="searchForm.keyword" placeholder="请输入案例标题" clearable></el-input>
                </el-form-item>
                <el-form-item label="关联服务">
                    <el-select v-model="searchForm.service_id" placeholder="请选择服务" clearable>
                        <el-option label="全部" value=""></el-option>
                        <el-option
                            v-for="service in services"
                            :key="service.id"
                            :label="service.name"
                            :value="service.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>

            <!-- 案例列表 -->
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column prop="title" label="案例标题" width="200"></el-table-column>
                <el-table-column label="案例图片" width="100">
                    <template slot-scope="scope">
                        <img v-if="scope.row.image" :src="scope.row.image" class="case-image" />
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="service_name" label="关联服务" width="150"></el-table-column>
                <el-table-column prop="seo_url" label="SEO URL" width="150"></el-table-column>
                <el-table-column prop="publish_date" label="发布日期" width="120"></el-table-column>
                <el-table-column prop="publisher" label="作者" width="100"></el-table-column>
                <el-table-column label="推荐" width="80">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.is_recommend ? 'success' : 'info'" size="small">
                            {{ scope.row.is_recommend ? '是' : '否' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="editCase(scope.row.id)">编辑</el-button>
                        <el-button size="mini" type="danger" @click="deleteCase(scope.row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="20"
                layout="total, prev, pager, next, jumper"
                :total="total"
                style="margin-top: 20px; text-align: center;">
            </el-pagination>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    tableData: <?php echo json_encode($List->items()); ?>,
                    currentPage: <?php echo $List->currentPage(); ?>,
                    total: <?php echo $List->total(); ?>,
                    services: <?php echo json_encode($services); ?>,
                    searchForm: {
                        keyword: '<?php echo $keyword ?? ""; ?>',
                        service_id: '<?php echo $service_id ?? ""; ?>'
                    }
                }
            },
            methods: {
                search() {
                    const params = new URLSearchParams();
                    if (this.searchForm.keyword) {
                        params.append('keyword', this.searchForm.keyword);
                    }
                    if (this.searchForm.service_id) {
                        params.append('service_id', this.searchForm.service_id);
                    }
                    window.location.href = "{:url('index')}?" + params.toString();
                },
                resetSearch() {
                    this.searchForm.keyword = '';
                    this.searchForm.service_id = '';
                    window.location.href = "{:url('index')}";
                },
                handleCurrentChange(page) {
                    const params = new URLSearchParams(window.location.search);
                    params.set('page', page);
                    window.location.href = "{:url('index')}?" + params.toString();
                },
                addCase() {
                    window.location.href = "{:url('add')}";
                },
                editCase(id) {
                    window.location.href = "{:url('edit')}?id=" + id;
                },
                deleteCase(id) {
                    this.$confirm('确定要删除这个案例吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.post("{:url('del')}", { id: id })
                            .then(response => {
                                if (response.data.code === 1) {
                                    this.$message.success(response.data.msg);
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 1000);
                                } else {
                                    this.$message.error(response.data.msg);
                                }
                            })
                            .catch(error => {
                                this.$message.error('删除失败');
                            });
                    });
                }
            }
        });
    </script>
</body>
</html>
