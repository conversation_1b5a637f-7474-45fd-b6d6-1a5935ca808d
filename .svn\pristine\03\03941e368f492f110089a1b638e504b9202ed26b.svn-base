<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Homepage extends Common
{
    // 优化方向管理
    public function optimizationIndex(){
        $keyword = input('keyword', '');
        $where = [];

        if ($keyword) {
            $where[] = ['title', 'like', '%' . $keyword . '%'];
        }

        $List = Db::name('optimization_direction')
            ->where($where)
            ->order("sort asc, id desc")
            ->paginate([
                'query'     =>  request()->param(),
                'list_rows' => 20,
            ]);

        return view("homepage/optimization/index", [
            "List" => $List,
            "keyword" => $keyword
        ]);
    }

    public function optimizationAdd(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['title']){
                $this->error("标题不能为空！");
            }

            // 处理文件上传
            if($_FILES['icon']['name']) $data['icon'] = $this->upload(request()->file("icon"));

            $s = Db::name("optimization_direction")->insertGetId($data);
            if($s){
                $this->success('添加成功');
            } else {
                $this->error("添加失败，请重试");
            }
        } else {
            return view("homepage/optimization/add");
        }
    }

    public function optimizationEdit(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['title']){
                $this->error("标题不能为空！");
            }

            // 处理文件上传
            if($_FILES['icon']['name']) $data['icon'] = $this->upload(request()->file("icon"));

            $s = Db::name("optimization_direction")->where("id", $data['id'])->save($data);
            if($s) {
                $this->success('修改成功');
            } else {
                $this->error("修改失败，请重试！");
            }

        } else {
            $id = input('id');
            $getone = Db::name("optimization_direction")->where("id", $id)->find();

            if (!$getone) {
                $this->error("记录不存在");
            }

            return view("homepage/optimization/edit", [
                "getone" => $getone
            ]);
        }
    }

    public function optimizationDel(){
        $id = input('id');
        $s = Db::name("optimization_direction")->where("id", $id)->delete();
        if($s){
            $this->success("删除成功");
        }else{
            $this->error("删除失败");
        }
    }

    // 本质区别管理
    public function differenceIndex(){
        $keyword = input('keyword', '');
        $where = [];

        if ($keyword) {
            $where[] = ['name', 'like', '%' . $keyword . '%'];
        }

        $List = Db::name('difference')
            ->where($where)
            ->order("sort asc, id desc")
            ->paginate([
                'query'     =>  request()->param(),
                'list_rows' => 20,
            ]);

        return view("homepage/difference/index", [
            "List" => $List,
            "keyword" => $keyword
        ]);
    }

    public function differenceAdd(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("名称不能为空！");
            }

            // 处理文件上传
            if($_FILES['icon']['name']) $data['icon'] = $this->upload(request()->file("icon"));

            $s = Db::name("difference")->insertGetId($data);
            if($s){
                $this->success('添加成功');
            } else {
                $this->error("添加失败，请重试");
            }
        } else {
            return view("homepage/difference/add");
        }
    }

    public function differenceEdit(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("名称不能为空！");
            }

            // 处理文件上传
            if($_FILES['icon']['name']) $data['icon'] = $this->upload(request()->file("icon"));

            $s = Db::name("difference")->where("id", $data['id'])->save($data);
            if($s) {
                $this->success('修改成功');
            } else {
                $this->error("修改失败，请重试！");
            }

        } else {
            $id = input('id');
            $getone = Db::name("difference")->where("id", $id)->find();

            if (!$getone) {
                $this->error("记录不存在");
            }

            return view("homepage/difference/edit", [
                "getone" => $getone
            ]);
        }
    }

    public function differenceDel(){
        $id = input('id');
        $s = Db::name("difference")->where("id", $id)->delete();
        if($s){
            $this->success("删除成功");
        }else{
            $this->error("删除失败");
        }
    }

    // 价值列表管理
    public function valuesIndex(){
        $keyword = input('keyword', '');
        $where = [];

        if ($keyword) {
            $where[] = ['title', 'like', '%' . $keyword . '%'];
        }

        $List = Db::name('values')
            ->where($where)
            ->order("sort asc, id desc")
            ->paginate([
                'query'     =>  request()->param(),
                'list_rows' => 20,
            ]);

        return view("homepage/values/index", [
            "List" => $List,
            "keyword" => $keyword
        ]);
    }

    public function valuesAdd(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['title']){
                $this->error("标题不能为空！");
            }

            $s = Db::name("values")->insertGetId($data);
            if($s){
                $this->success('添加成功');
            } else {
                $this->error("添加失败，请重试");
            }
        } else {
            return view("homepage/values/add");
        }
    }

    public function valuesEdit(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['title']){
                $this->error("标题不能为空！");
            }

            $s = Db::name("values")->where("id", $data['id'])->save($data);
            if($s) {
                $this->success('修改成功');
            } else {
                $this->error("修改失败，请重试！");
            }

        } else {
            $id = input('id');
            $getone = Db::name("values")->where("id", $id)->find();

            if (!$getone) {
                $this->error("记录不存在");
            }

            return view("homepage/values/edit", [
                "getone" => $getone
            ]);
        }
    }

    public function valuesDel(){
        $id = input('id');
        $s = Db::name("values")->where("id", $id)->delete();
        if($s){
            $this->success("删除成功");
        }else{
            $this->error("删除失败");
        }
    }
}
