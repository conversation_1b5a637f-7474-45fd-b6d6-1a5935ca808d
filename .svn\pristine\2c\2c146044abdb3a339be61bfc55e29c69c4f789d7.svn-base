<?php
declare (strict_types = 1);

namespace app\admin\controller;

use think\facade\Db;

class Role extends Common
{
    // 角色列表
    public function index()
    {
        $list = Db::name('role')
            ->order('id desc')
            ->paginate(['list_rows' => 15]);

        return view('', ['list' => $list]);
    }

    // 添加角色
    public function add()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if (!$data['name']) {
                return json(['code' => 0, 'msg' => '角色名称不能为空']);
            }

            if (Db::name('role')->where('name', $data['name'])->find()) {
                return json(['code' => 0, 'msg' => '角色名称已存在']);
            }

            $data['permissions'] = json_encode($data['permissions'] ?? []);
            $data['create_time'] = date('Y-m-d H:i:s');

            $result = Db::name('role')->insert($data);

            if ($result) {
                return json(['code' => 1, 'msg' => '添加成功']);
            } else {
                return json(['code' => 0, 'msg' => '添加失败']);
            }
        }

        return view();
    }

    // 编辑角色
    public function edit()
    {
        $id = input('id');

        if ($this->request->isPost()) {
            $data = input('post.');

            if (!$data['name']) {
                return json(['code' => 0, 'msg' => '角色名称不能为空']);
            }

            $exists = Db::name('role')
                ->where([
                    ['name', '=', $data['name']],
                    ['id', '<>', $id]
                ])
                ->find();

            if ($exists) {
                return json(['code' => 0, 'msg' => '角色名称已存在']);
            }

            $data['permissions'] = json_encode($data['permissions'] ?? []);
            $data['update_time'] = date('Y-m-d H:i:s');

            $result = Db::name('role')->where('id', $id)->update($data);

            if ($result !== false) {
                return json(['code' => 1, 'msg' => '更新成功']);
            } else {
                return json(['code' => 0, 'msg' => '更新失败']);
            }
        }

        $role = Db::name('role')->find($id);
        $role['permissions'] = json_decode($role['permissions'] ?? '[]', true);

        return view('', ['role' => $role]);
    }

    // 删除角色
    public function delete()
    {
        $id = input('id');

        // 检查是否有管理员使用此角色
        $adminCount = Db::name('admin')->where('role_id', $id)->count();
        if ($adminCount > 0) {
            return json(['code' => 0, 'msg' => '该角色下还有管理员，无法删除']);
        }

        $result = Db::name('role')->delete($id);

        if ($result) {
            return json(['code' => 1, 'msg' => '删除成功']);
        } else {
            return json(['code' => 0, 'msg' => '删除失败']);
        }
    }
}