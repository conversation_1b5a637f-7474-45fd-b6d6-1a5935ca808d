<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>修改密码</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        .password-container { padding: 20px; }
        .password-form { max-width: 500px; margin: 0 auto; }
    </style>
</head>
<body>
    <div id="app" class="password-container">
        <el-card>
            <div slot="header">
                <span>修改密码</span>
            </div>
            <el-form :model="form" :rules="rules" ref="form" label-width="100px" class="password-form">
                <el-form-item label="原密码" prop="old_password">
                    <el-input type="password" v-model="form.old_password" placeholder="请输入原密码"></el-input>
                </el-form-item>
                <el-form-item label="新密码" prop="new_password">
                    <el-input type="password" v-model="form.new_password" placeholder="请输入新密码"></el-input>
                </el-form-item>
                <el-form-item label="确认密码" prop="confirm_password">
                    <el-input type="password" v-model="form.confirm_password" placeholder="请再次输入新密码"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="loading">确认修改</el-button>
                    <el-button @click="resetForm">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    loading: false,
                    form: {
                        old_password: '',
                        new_password: '',
                        confirm_password: ''
                    },
                    rules: {
                        old_password: [
                            { required: true, message: '请输入原密码', trigger: 'blur' }
                        ],
                        new_password: [
                            { required: true, message: '请输入新密码', trigger: 'blur' },
                            { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
                        ],
                        confirm_password: [
                            { required: true, message: '请确认新密码', trigger: 'blur' },
                            { validator: this.validateConfirmPassword, trigger: 'blur' }
                        ]
                    }
                }
            },
            methods: {
                validateConfirmPassword(rule, value, callback) {
                    if (value !== this.form.new_password) {
                        callback(new Error('两次输入的密码不一致'));
                    } else {
                        callback();
                    }
                },
                submitForm() {
                    this.$refs.form.validate((valid) => {
                        if (valid) {
                            this.loading = true;
                            axios.post('/admin/admin/myeditpwd', this.form)
                                .then(response => {
                                    this.loading = false;
                                    if (response.data.code === 1) {
                                        this.$message.success(response.data.msg);
                                        this.resetForm();
                                    } else {
                                        this.$message.error(response.data.msg);
                                    }
                                })
                                .catch(error => {
                                    this.loading = false;
                                    this.$message.error('操作失败，请重试');
                                });
                        }
                    });
                },
                resetForm() {
                    this.$refs.form.resetFields();
                }
            }
        });
    </script>
</body>
</html>