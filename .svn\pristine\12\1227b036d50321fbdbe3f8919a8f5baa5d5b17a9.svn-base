<!-- Element UI CSS -->
<link rel="stylesheet" href="__STATIC__/lib/element-ui/theme-chalk/index.css">
<!-- CKEditor5 CSS -->
<link rel="stylesheet" href="__STATIC__/dist/ckeditor5/ckeditor5.css" crossorigin>
<link rel="stylesheet" href="__STATIC__/ckeditor5/ckeditor5.css">
<!-- 公共样式 -->
<link rel="stylesheet" href="__CSS__/common.css">

<!-- 公共样式 -->
<style>
    /* 通用容器样式 */
    .admin-container {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }

    /* 文件上传样式 */
    .upload-demo {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    /* 表单样式 */
    .form-container {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    /* 按钮组样式 */
    .button-group {
        text-align: center;
        margin-top: 20px;
        padding: 20px 0;
        border-top: 1px solid #ebeef5;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .admin-container {
            padding: 10px;
        }

        .button-group .el-button {
            margin: 5px;
        }
    }

    /* 加载状态 */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    /* 上传按钮样式优化 */
    .file-upload-container {
        display: flex;
        /* flex-direction: column; */
        gap: 10px;
    }
    .upload-btn {
        background-color: #409EFF;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 20px;
        font-size: 14px;
        cursor: pointer;
        min-width: 120px;
        white-space: nowrap;
        transition: background-color 0.3s;
    }
    .upload-btn:hover {
        background-color: #66b1ff;
    }
    .upload-btn:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
    .search-form { margin-bottom: 20px; }
    .el-table { margin-top: 20px; }
    .case-image { width: 60px; height: 40px; object-fit: cover; border-radius: 4px; }
    .content-preview { max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
</style>