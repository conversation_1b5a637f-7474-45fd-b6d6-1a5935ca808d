<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>添加服务</title>
    {include file="common/head" /}
    <style>
        .form-section { margin-bottom: 30px; }
        .section-title { font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #303133; }
        .dynamic-item {
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            position: relative;
        }
        .remove-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
        /* 确保富文本编辑器不会遮挡删除按钮 */
        .ck-editor {
            position: relative;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>添加服务</span>
                <el-button style="float: right;" @click="goBack">返回列表</el-button>
            </div>

            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="section-title">基本信息</div>

                    <el-form-item label="服务名称" prop="name">
                        <el-input v-model="form.name" placeholder="请输入服务名称"></el-input>
                    </el-form-item>

                    <el-form-item label="服务标题" prop="title">
                        <el-input v-model="form.title" placeholder="请输入服务标题"></el-input>
                    </el-form-item>

                    <el-form-item label="SEO URL" prop="seo_url">
                        <el-input v-model="form.seo_url" placeholder="留空自动生成"></el-input>
                    </el-form-item>

                    <el-form-item label="图标">
                        <div class="file-upload-container icon-preview">
                            <input type="file" name="icon" accept="image/*">
                            <button type="button" class="upload-btn">选择图标</button>
                            <div class="file-preview">
                                <img v-if="form.icon" :src="form.icon" class="preview-image" style="display: block;">
                            </div>
                        </div>
                    </el-form-item>

                    <el-form-item label="背景图片">
                        <div class="file-upload-container icon-preview">
                            <input type="file" name="backcloth" accept="image/*">
                            <button type="button" class="upload-btn">选择背景图片</button>
                            <div class="file-preview">
                                <img v-if="form.backcloth" :src="form.backcloth" class="preview-image" style="display: block;">
                            </div>
                        </div>
                    </el-form-item>

                    <el-form-item label="服务图片">
                        <div class="file-upload-container icon-preview">
                            <input type="file" name="image" accept="image/*">
                            <button type="button" class="upload-btn">选择服务图片</button>
                            <div class="file-preview">
                                <img v-if="form.image" :src="form.image" class="preview-image" style="display: block;">
                            </div>
                        </div>
                    </el-form-item>

                    <el-form-item label="排序">
                        <el-input-number v-model="form.sort" :min="0" :max="999"></el-input-number>
                    </el-form-item>

                    <el-form-item label="推荐">
                        <el-switch v-model="form.is_recommend"></el-switch>
                    </el-form-item>

                    <el-form-item label="服务内容">
                        <div class="ckeditor-textarea" data-name="content"></div>
                    </el-form-item>
                </div>

                <!-- 服务策略 -->
                <div class="form-section">
                    <div class="section-title">
                        服务策略
                        <el-button type="primary" size="mini" @click="addStrategy" style="margin-left: 10px;">添加策略</el-button>
                    </div>

                    <div v-for="(strategy, index) in form.strategies" :key="'strategy-' + index" class="dynamic-item">
                        <el-button type="danger" size="mini" class="remove-btn" @click="removeStrategy(index)">删除</el-button>

                        <el-form-item :label="'策略标题 ' + (index + 1)">
                            <el-input v-model="strategy.title" placeholder="请输入策略标题"></el-input>
                        </el-form-item>

                        <el-form-item :label="'策略内容 ' + (index + 1)">
                            <div class="ckeditor-textarea" :data-name="'strategy_content_' + index" :data-content="strategy.content"></div>
                        </el-form-item>

                        <el-form-item :label="'排序 ' + (index + 1)">
                            <el-input-number v-model="strategy.sort" :min="0" :max="999"></el-input-number>
                        </el-form-item>
                    </div>
                </div>

                <!-- 服务优势 -->
                <div class="form-section">
                    <div class="section-title">
                        服务优势
                        <el-button type="primary" size="mini" @click="addAdvantage" style="margin-left: 10px;">添加优势</el-button>
                    </div>

                    <div v-for="(advantage, index) in form.advantages" :key="'advantage-' + index" class="dynamic-item">
                        <el-button type="danger" size="mini" class="remove-btn" @click="removeAdvantage(index)">删除</el-button>

                        <el-form-item :label="'优势图标 ' + (index + 1)">
                            <div class="file-upload-container icon-preview">
                                <input type="file" :name="'advantage_icon_' + index" accept="image/*">
                                <button type="button" class="upload-btn">选择图标</button>
                                <div class="file-preview">
                                    <img v-if="advantage.icon" :src="advantage.icon" class="preview-image" style="display: block;">
                                </div>
                            </div>
                        </el-form-item>

                        <el-form-item :label="'优势标题 ' + (index + 1)">
                            <el-input v-model="advantage.title" placeholder="请输入优势标题"></el-input>
                        </el-form-item>

                        <el-form-item :label="'优势内容 ' + (index + 1)">
                            <div class="ckeditor-textarea" :data-name="'advantage_content_' + index" :data-content="advantage.content"></div>
                        </el-form-item>

                        <el-form-item :label="'排序 ' + (index + 1)">
                            <el-input-number v-model="advantage.sort" :min="0" :max="999"></el-input-number>
                        </el-form-item>
                    </div>
                </div>

                <!-- 服务流程 -->
                <div class="form-section">
                    <div class="section-title">
                        服务流程
                        <el-button type="primary" size="mini" @click="addProcess" style="margin-left: 10px;">添加流程</el-button>
                    </div>

                    <div v-for="(process, index) in form.processes" :key="'process-' + index" class="dynamic-item">
                        <el-button type="danger" size="mini" class="remove-btn" @click="removeProcess(index)">删除</el-button>

                        <el-form-item :label="'流程标题 ' + (index + 1)">
                            <el-input v-model="process.title" placeholder="请输入流程标题"></el-input>
                        </el-form-item>

                        <el-form-item :label="'流程内容 ' + (index + 1)">
                            <div class="ckeditor-textarea" :data-name="'process_content_' + index" :data-content="process.content"></div>
                        </el-form-item>

                        <el-form-item :label="'排序 ' + (index + 1)">
                            <el-input-number v-model="process.sort" :min="0" :max="999"></el-input-number>
                        </el-form-item>
                    </div>
                </div>

                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="submitting">提交</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    {include file="common/foot" /}

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    submitting: false,
                    form: {
                        name: '',
                        title: '',
                        seo_url: '',
                        content: '',
                        sort: 50,
                        is_recommend: false,
                        strategies: [],
                        advantages: [],
                        processes: []
                    },
                    rules: {
                        name: [
                            { required: true, message: '请输入服务名称', trigger: 'blur' }
                        ],
                        title: [
                            { required: true, message: '请输入服务标题', trigger: 'blur' }
                        ]
                    }
                }
            },
            methods: {
                addStrategy() {
                    const index = this.form.strategies.length;
                    this.form.strategies.push({
                        title: '',
                        content: '',
                        sort: 50
                    });
                    this.$nextTick(() => {
                        // 只初始化新添加的编辑器
                        const newEditorSelector = `[data-name="strategy_content_${index}"]`;
                        if (window.AdminUtils && window.AdminUtils.initEditors) {
                            window.AdminUtils.initEditors(newEditorSelector);
                        }
                    });
                },
                removeStrategy(index) {
                    this.form.strategies.splice(index, 1);
                },
                addAdvantage() {
                    const index = this.form.advantages.length;
                    this.form.advantages.push({
                        icon: '',
                        title: '',
                        content: '',
                        sort: 50
                    });
                    this.$nextTick(() => {
                        // 只初始化新添加的编辑器
                        const newEditorSelector = `[data-name="advantage_content_${index}"]`;
                        if (window.AdminUtils && window.AdminUtils.initEditors) {
                            window.AdminUtils.initEditors(newEditorSelector);
                        }
                        // 初始化新添加的文件上传功能
                        const newUploadContainer = document.querySelector(`[name="advantage_icon_${index}"]`)?.closest('.file-upload-container');
                        if (newUploadContainer && window.adminCommon) {
                            const input = newUploadContainer.querySelector('input[type="file"]');
                            const button = newUploadContainer.querySelector('.upload-btn');
                            const preview = newUploadContainer.querySelector('.file-preview');

                            if (input && button) {
                                const name = input.getAttribute('name');

                                // 注册到fileUploads Map中
                                window.adminCommon.fileUploads.set(name, {
                                    input,
                                    button,
                                    preview,
                                    file: null,
                                    previewUrl: null
                                });

                                // 绑定点击事件
                                button.addEventListener('click', () => {
                                    input.click();
                                });

                                // 绑定文件选择事件
                                input.addEventListener('change', (event) => {
                                    window.adminCommon.handleFileChange(event, name, preview);
                                });
                            }
                        }
                    });
                },
                removeAdvantage(index) {
                    this.form.advantages.splice(index, 1);
                },
                addProcess() {
                    const index = this.form.processes.length;
                    this.form.processes.push({
                        title: '',
                        content: '',
                        sort: 50
                    });
                    this.$nextTick(() => {
                        // 只初始化新添加的编辑器
                        const newEditorSelector = `[data-name="process_content_${index}"]`;
                        if (window.AdminUtils && window.AdminUtils.initEditors) {
                            window.AdminUtils.initEditors(newEditorSelector);
                        }
                    });
                },
                removeProcess(index) {
                    this.form.processes.splice(index, 1);
                },
                submitForm() {
                    window.AdminUtils.submitForm(this, {
                        url: "{:url('add')}",
                        formData: this.form,
                        fileFields: ['icon', 'backcloth', 'image'],
                        redirectUrl: "{:url('index')}",
                        beforeSubmit: (formData) => {
                            // 在提交前处理关联数据
                            formData.strategies = JSON.stringify(this.form.strategies);
                            formData.advantages = JSON.stringify(this.form.advantages);
                            formData.processes = JSON.stringify(this.form.processes);
                        }
                    });
                },
                goBack() {
                    window.AdminUtils.goBack("{:url('index')}");
                }
            }
        });
    </script>
</body>
</html>
