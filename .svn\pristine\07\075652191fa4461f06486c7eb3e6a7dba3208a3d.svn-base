<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>添加策略流程</title>
    {include file="common/head" /}
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>添加策略流程</span>
                <el-button style="float: right;" @click="goBack">返回列表</el-button>
            </div>

            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="流程标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入流程标题"></el-input>
                </el-form-item>

                <el-form-item label="排序">
                    <el-input-number v-model="form.sort" :min="0" :max="999"></el-input-number>
                </el-form-item>

                <el-form-item label="流程内容">
                    <div class="ckeditor-textarea" data-name="content"></div>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="submitting">提交</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    submitting: false,
                    contentEditor: null,
                    form: {
                        title: '',
                        content: '',
                        sort: 50
                    },
                    rules: {
                        title: [
                            { required: true, message: '请输入流程标题', trigger: 'blur' }
                        ]
                    }
                }
            },
            methods: {
                submitForm() {
                    window.AdminUtils.submitForm(this, {
                        url: "{:url('processAdd')}",
                        formData: this.form,
                        redirectUrl: "{:url('processIndex')}",
                    });
                },
                goBack() {
                    window.AdminUtils.goBack("{:url('processIndex')}");
                }
            }
        });
    </script>
</body>
</html>
