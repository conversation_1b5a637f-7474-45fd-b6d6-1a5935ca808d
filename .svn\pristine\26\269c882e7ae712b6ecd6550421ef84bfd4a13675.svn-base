<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>价值列表管理</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        .values-container { padding: 20px; }
        .search-form { margin-bottom: 20px; }
        .el-table { margin-top: 20px; }
        .content-preview { max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
    </style>
</head>
<body>
    <div id="app" class="values-container">
        <el-card>
            <div slot="header">
                <span>价值列表管理</span>
                <el-button style="float: right;" type="primary" @click="addValues">添加价值</el-button>
            </div>

            <!-- 搜索表单 -->
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="关键词">
                    <el-input v-model="searchForm.keyword" placeholder="请输入标题" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>

            <!-- 价值列表 -->
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column prop="title" label="标题" width="250"></el-table-column>
                <el-table-column label="内容" width="400">
                    <template slot-scope="scope">
                        <div class="content-preview">{{ getContentPreview(scope.row.content) }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="sort" label="排序" width="80"></el-table-column>
                <el-table-column label="操作" width="200">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="editValues(scope.row.id)">编辑</el-button>
                        <el-button size="mini" type="danger" @click="deleteValues(scope.row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="20"
                layout="total, prev, pager, next, jumper"
                :total="total"
                style="margin-top: 20px; text-align: center;">
            </el-pagination>
        </el-card>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    tableData: <?php echo json_encode($List->items()); ?>,
                    currentPage: <?php echo $List->currentPage(); ?>,
                    total: <?php echo $List->total(); ?>,
                    searchForm: {
                        keyword: '<?php echo $keyword ?? ""; ?>'
                    }
                }
            },
            methods: {
                // 获取内容预览（去除HTML标签，限制字符数）
                getContentPreview(content) {
                    if (!content) return '-';

                    // 去除HTML标签
                    const textContent = content.replace(/<[^>]*>/g, '');

                    // 限制显示字符数
                    const maxLength = 60;
                    if (textContent.length > maxLength) {
                        return textContent.substring(0, maxLength) + '...';
                    }

                    return textContent || '-';
                },

                search() {
                    const params = new URLSearchParams();
                    if (this.searchForm.keyword) {
                        params.append('keyword', this.searchForm.keyword);
                    }
                    window.location.href = "{:url('valuesIndex')}?" + params.toString();
                },
                resetSearch() {
                    this.searchForm.keyword = '';
                    window.location.href = "{:url('valuesIndex')}";
                },
                handleCurrentChange(page) {
                    const params = new URLSearchParams(window.location.search);
                    params.set('page', page);
                    window.location.href = "{:url('valuesIndex')}?" + params.toString();
                },
                addValues() {
                    window.location.href = "{:url('valuesAdd')}";
                },
                editValues(id) {
                    window.location.href = "{:url('valuesEdit')}?id=" + id;
                },
                deleteValues(id) {
                    this.$confirm('确定要删除这个价值吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.post("{:url('valuesDel')}", { id: id })
                            .then(response => {
                                if (response.data.code === 1) {
                                    this.$message.success(response.data.msg);
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 1000);
                                } else {
                                    this.$message.error(response.data.msg);
                                }
                            })
                            .catch(error => {
                                this.$message.error('删除失败');
                            });
                    });
                }
            }
        });
    </script>
</body>
</html>
