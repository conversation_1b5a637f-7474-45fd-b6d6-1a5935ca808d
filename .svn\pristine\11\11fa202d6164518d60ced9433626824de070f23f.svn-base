<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;

class Service extends Common
{
    public function index()
    {
        $seo_url = input("url");

        $getone = Db::name("Service_category")->where("seo_url", $seo_url)->find();
        if (!empty($getone)) {
            //服务分类页
            $advantages_list = json_decode($getone["advantages_list"] ?? '', true) ?? [];

            $tdk = [
                "seo_title"=> $getone["seo_title"],
                "seo_description"=> $getone["seo_description"],
                "seo_keywords"=> $getone["seo_keywords"],
            ];

            //服务列表
            $service = Db::name("Service")->where("service_category", $getone["id"])->order("sort asc")
            ->field("id, seo_url, name, icon, icon_color, content")
            ->select();

            //案例列表
            $cases = Db::name("Cases")->where("service_category", $getone["id"])->order("publish_date desc")->field("id, seo_url, title, content, image")->limit(4)->select();

            //博客
            //SEO Algorithm
            $algorithm = Db::name('Blog')->where(['blog_type'=>1, 'service_category'=>$getone['id']])->order("publish_date desc")->limit(8)->column("id, seo_url, title, content");
            //Technical Encyclopedia
            $bkCategory = Db::name("Blog_category")->where('blog_type', 2)->order("sort asc")->limit(4)->select();
            $bkCategory->each(function ($item, $key) use($getone) {
                $item['cases'] = Db::name('Blog')->where(['blog_category'=>$item['id'], 'service_category'=>$getone['id']])->order("publish_date desc")->column('id, seo_url, title');
                return $item;
            });
            //SEO Tools
            $tools = Db::name('Blog')->where(['blog_type'=>3, 'service_category'=>$getone['id']])->field("id, seo_url, title, image, content")->limit(4)->select();

            //最新新闻
            $news = Db::name('Blog')->where(['blog_type'=>4, 'service_category'=>$getone['id']])->order("publish_date desc")->field("id, seo_url, title")->limit(6)->select();

            //问答
            $faqs = Db::name("Faqs")->where("service_category", $getone['id'])->order("publish_date desc")->select();

            //其他服务
            $otherServices = Db::name("Service_category")->where("id", "<>", $getone['id'])->order("sort asc")->field("id, seo_url, name, icon, content")->select();

            return view("", [
                "getone" => $getone,
                "advantages_list"=> $advantages_list,
                "tdk" => $tdk,
                "service" => $service,
                "cases" => $cases,
                "algorithm" => $algorithm,
                "bkCategory" => $bkCategory,
                "tools" => $tools,
                "news" => $news,
                "faqs" => $faqs,
                "otherServices" => $otherServices,
            ]);
        } else {
            //服务详情页
            $getone = Db::name("Service")->where("seo_url", $seo_url)->find();

            $serviceCategory = Db::name("Service_category")->where("id", $getone['service_category'])->find();

            $value_list = json_decode($getone["value_list"] ?? '', true) ?? [];
            $core_list = json_decode($getone["core_list"] ?? '', true) ?? [];
            $experience_list = json_decode($getone["experience_list"] ?? '', true) ?? [];
            $advantage_list = json_decode($getone["advantage_list"] ?? '', true) ?? [];

            $tdk = [
                "seo_title"=> $getone["seo_title"]?$getone["seo_title"]:strip_tags($getone["title"]),
                "seo_description"=> $getone["seo_description"],
                "seo_keywords"=> $getone["seo_keywords"],
            ];

            //案例列表
            $cases = Db::name("Cases")->where("service_id", $getone["id"])->order("publish_date desc")->field("id, seo_url, name, title, content, image")->select();
            $casesColor =  Db::name("Service_category")->where("id", $getone["service_category"])->value("color");

            //博客
            //SEO Algorithm
            $algorithm = Db::name('Blog')->where(['blog_type'=>1, 'service_category'=>$getone['id']])->order("publish_date desc")->limit(8)->column("id, seo_url, title, content");
            //Technical Encyclopedia
            $bkCategory = Db::name("Blog_category")->where('blog_type', 2)->order("sort asc")->limit(4)->select();
            $bkCategory->each(function ($item, $key) use($getone) {
                $item['cases'] = Db::name('Blog')->where(['blog_category'=>$item['id'], 'service_category'=>$getone['id']])->order("publish_date desc")->column('id, seo_url, title');
                return $item;
            });
            //SEO Tools
            $tools = Db::name('Blog')->where(['blog_type'=>3, 'service_category'=>$getone['id']])->field("id, seo_url, title, image, content")->limit(4)->select();

            //其他服务
            $where = [
                ["id", "<>", $getone["id"]],
                ["service_category","=", $getone["service_category"]],
            ];
            $otherServices = Db::name("Service")->where($where)->order("sort asc")->column("id, seo_url, name, icon, content, service_category");
            if(count($otherServices)<3){
                $restServices = Db::name("Service")->where("service_category", "<>", $getone['service_category'])->orderRaw('rand()')->limit(3-count($otherServices))->column("id, seo_url, name, icon, content, service_category");
                $otherServices = array_merge($otherServices, $restServices);
            }

            return view("details", [
                "serviceCategory" => $serviceCategory,
                "getone"=> $getone,
                "value_list" => $value_list,
                "core_list" => $core_list,
                "experience_list"=> $experience_list,
                "advantage_list"=> $advantage_list,
                "tdk" => $tdk,
                "cases" => $cases,
                "casesColor" => $casesColor,
                "algorithm" => $algorithm,
                "bkCategory" => $bkCategory,
                "tools" => $tools,
                "otherServices" => $otherServices,
            ]);
        }

    }

}
