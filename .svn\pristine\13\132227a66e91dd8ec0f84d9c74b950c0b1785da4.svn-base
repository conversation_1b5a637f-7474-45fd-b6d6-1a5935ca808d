<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Service extends Common
{
    public function index(){
        $keyword = input('keyword', '');
        $where = [];

        if ($keyword) {
            $where[] = ['name|title', 'like', '%' . $keyword . '%'];
        }

        $List = Db::name('service')
            ->where($where)
            ->order("sort asc, id desc")
            ->paginate([
                'query'     =>  request()->param(),
                'list_rows' => 20,
            ]);

        return view("", [
            "List" => $List,
            "keyword" => $keyword
        ]);
    }

    public function add(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("服务名称不能为空！");
            }

            if(!trim($data['seo_url'])){
                $data['seo_url'] = seoFriendlyUrl($data['name']);
            }
            if(Db::name('service')->where("seo_url", $data['seo_url'])->find()){
                $this->error("SEO URL已存在！");
            }

            // 处理文件上传
            if($_FILES['icon']['name']) $data['icon'] = $this->upload(request()->file("icon"));
            if($_FILES['backcloth']['name']) $data['backcloth'] = $this->upload(request()->file("backcloth"));
            if($_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));

            $data['is_recommend'] = isset($data['is_recommend']) ? 1 : 0;

            // 开启事务
            Db::startTrans();
            try {
                $serviceId = Db::name("service")->insertGetId($data);

                if ($serviceId) {
                    // 保存策略数据
                    if (isset($data['strategies']) && !empty($data['strategies'])) {
                        $strategies = is_string($data['strategies']) ? json_decode($data['strategies'], true) : $data['strategies'];
                        if (is_array($strategies)) {
                            foreach ($strategies as $strategy) {
                                if (!empty($strategy['title'])) {
                                    Db::name('service_strategy')->insert([
                                        'service_id' => $serviceId,
                                        'title' => $strategy['title'],
                                        'content' => $strategy['content'] ?? '',
                                        'sort' => $strategy['sort'] ?? 50
                                    ]);
                                }
                            }
                        }
                    }

                    // 保存优势数据
                    if (isset($data['advantages']) && !empty($data['advantages'])) {
                        $advantages = is_string($data['advantages']) ? json_decode($data['advantages'], true) : $data['advantages'];
                        if (is_array($advantages)) {
                            foreach ($advantages as $advantage) {
                                if (!empty($advantage['title'])) {
                                    Db::name('service_advantage')->insert([
                                        'service_id' => $serviceId,
                                        'icon' => $advantage['icon'] ?? '',
                                        'title' => $advantage['title'],
                                        'content' => $advantage['content'] ?? '',
                                        'sort' => $advantage['sort'] ?? 50
                                    ]);
                                }
                            }
                        }
                    }

                    // 保存流程数据
                    if (isset($data['processes']) && !empty($data['processes'])) {
                        $processes = is_string($data['processes']) ? json_decode($data['processes'], true) : $data['processes'];
                        if (is_array($processes)) {
                            foreach ($processes as $process) {
                                if (!empty($process['title'])) {
                                    Db::name('service_process')->insert([
                                        'service_id' => $serviceId,
                                        'title' => $process['title'],
                                        'content' => $process['content'] ?? '',
                                        'sort' => $process['sort'] ?? 50
                                    ]);
                                }
                            }
                        }
                    }

                    Db::commit();
                    $this->success('添加成功');
                } else {
                    Db::rollback();
                    $this->error("添加失败，请重试");
                }
            } catch (\Exception $e) {
                Db::rollback();
                $this->error("添加失败：" . $e->getMessage());
            }
        } else {
            return view();
        }
    }

    public function edit(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("服务名称不能为空！");
            }

            if(!trim($data['seo_url'])){
                $data['seo_url'] = seoFriendlyUrl($data['name']);
            }
            $where = [
                ['id', '<>', $data['id']],
                ['seo_url', '=', $data['seo_url']],
            ];
            if(Db::name('service')->where($where)->find()){
                $this->error("SEO URL已存在！");
            }

            // 处理文件上传
            if($_FILES['icon']['name']) $data['icon'] = $this->upload(request()->file("icon"));
            if($_FILES['backcloth']['name']) $data['backcloth'] = $this->upload(request()->file("backcloth"));
            if($_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));

            $data['is_recommend'] = isset($data['is_recommend']) ? 1 : 0;

            // 开启事务
            Db::startTrans();
            try {
                Db::name("service")->where("id", $data['id'])->save($data);

                // 删除原有的关联数据
                Db::name('service_strategy')->where('service_id', $data['id'])->delete();
                Db::name('service_advantage')->where('service_id', $data['id'])->delete();
                Db::name('service_process')->where('service_id', $data['id'])->delete();

                // 重新保存策略数据
                if (isset($data['strategies']) && !empty($data['strategies'])) {
                    $strategies = is_string($data['strategies']) ? json_decode($data['strategies'], true) : $data['strategies'];
                    if (is_array($strategies)) {
                        foreach ($strategies as $strategy) {
                            if (!empty($strategy['title'])) {
                                Db::name('service_strategy')->insert([
                                    'service_id' => $data['id'],
                                    'title' => $strategy['title'],
                                    'content' => $strategy['content'] ?? '',
                                    'sort' => $strategy['sort'] ?? 50
                                ]);
                            }
                        }
                    }
                }

                // 重新保存优势数据
                if (isset($data['advantages']) && !empty($data['advantages'])) {
                    $advantages = is_string($data['advantages']) ? json_decode($data['advantages'], true) : $data['advantages'];
                    if (is_array($advantages)) {
                        foreach ($advantages as $advantage) {
                            if (!empty($advantage['title'])) {
                                Db::name('service_advantage')->insert([
                                    'service_id' => $data['id'],
                                    'icon' => $advantage['icon'] ?? '',
                                    'title' => $advantage['title'],
                                    'content' => $advantage['content'] ?? '',
                                    'sort' => $advantage['sort'] ?? 50
                                ]);
                            }
                        }
                    }
                }

                // 重新保存流程数据
                if (isset($data['processes']) && !empty($data['processes'])) {
                    $processes = is_string($data['processes']) ? json_decode($data['processes'], true) : $data['processes'];
                    if (is_array($processes)) {
                        foreach ($processes as $process) {
                            if (!empty($process['title'])) {
                                Db::name('service_process')->insert([
                                    'service_id' => $data['id'],
                                    'title' => $process['title'],
                                    'content' => $process['content'] ?? '',
                                    'sort' => $process['sort'] ?? 50
                                ]);
                            }
                        }
                    }
                }

                Db::commit();
                $this->success('修改成功');
            } catch (\Exception $e) {
                Db::rollback();
                $this->error("修改失败：" . $e->getMessage());
            }

        } else {
            $id = input('id');
            $getone = Db::name("service")->where("id", $id)->find();

            if (!$getone) {
                $this->error("服务不存在");
            }

            // 获取关联数据
            $strategies = Db::name('service_strategy')->where('service_id', $id)->order('sort asc')->select();
            $advantages = Db::name('service_advantage')->where('service_id', $id)->order('sort asc')->select();
            $processes = Db::name('service_process')->where('service_id', $id)->order('sort asc')->select();

            return view("", [
                "getone" => $getone,
                "strategies" => $strategies,
                "advantages" => $advantages,
                "processes" => $processes
            ]);
        }
    }

    public function del(){
        $id = input('id');

        // 开启事务
        Db::startTrans();
        try {
            // 删除关联数据
            Db::name('service_strategy')->where('service_id', $id)->delete();
            Db::name('service_advantage')->where('service_id', $id)->delete();
            Db::name('service_process')->where('service_id', $id)->delete();

            // 删除主数据
            $s = Db::name("service")->where("id", $id)->delete();

            if($s){
                Db::commit();
                $this->success("删除成功");
            } else {
                Db::rollback();
                $this->error("删除失败");
            }
        } catch (\Exception $e) {
            Db::rollback();
            $this->error("删除失败：" . $e->getMessage());
        }
    }
}
