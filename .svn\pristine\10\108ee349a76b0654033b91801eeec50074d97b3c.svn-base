<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SEO优化中修改文章标题会影响排名吗？ - GGSEO TOOLS工具分享</title>
    <!-- 页面描述 -->
    <meta name="description" content="本文详细探讨了在SEO优化过程中修改文章标题对网站排名的影响，以及如何正确调整标题以提升SEO效果，帮助您更好地理解标题优化策略。" />
    <!-- 关键词 -->
    <meta name="keywords" content="SEO标题优化,文章标题修改,SEO排名影响,网站优化技巧,GGSEO" />

    {include file="public:head"}
</head>

<body class="pt-[50px] md:pt-[100px]">
    {include file="public:header"}

    <section class="bg-[#f7f6fa] h-14 md:h-20">
        <div class="max-w-9/10 mx-auto container h-full flex items-center">
            <!-- 面包屑导航 -->
            <nav class="breadcrumb-nav">
                <ol
                    class="flex items-center before:content-['\25B6'] before:text-[#001045] before:mr-1 before:text-[10px] gap-x-2 uppercase">
                    <li class="flex items-center gap-x-2">
                        <a href="/" class="text-[#001045]">HOME</a> \
                    </li>
                    <li class="flex items-center gap-x-2">
                        <span class="text-[#001045]">Blog</span> \
                    </li>
                    <li class="flex items-center max-w-[100px] md:max-w-2xs">
                        <span class="text-[#999] line-clamp-1">SEO TOOLS</span>
                    </li>
                </ol>
            </nav>
        </div>
    </section>

    <section
        class="bg-[url('__IMG__/tools_bg_m.jpg')] bg-no-repeat bg-center h-[300px] md:h-[500px] md:bg-[url('__IMG__/tools_bg.jpg')]">
        <div class="max-w-9/10 mx-auto container h-full flex items-center">
            <div class="md:max-w-9/10 mx-auto w-full">
                {$tdk.title|raw}
                <div class="w-full flex items-center gap-x-2 md:gap-x-5">
                    <div class="text-center w-full text-base text-white">
                        {$tdk.content|raw}
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="bg-[#f7f6fa] py-8">
        <div class="max-w-9/10 mx-auto container">
            <div class="screen-sorting bg-[#fff] rounded-sm px-5 py-4 mb-5 md:px-12 md:py-9">
                <div class="screen-sorting-list flex flex-col gap-y-3">
                    <div class="screen-sorting-list-item flex flex-row flex-wrap gap-x-2">
                        <div class="text-[#001045] text-sm py-1 md:text-base bold-text">
                            Seo tools:
                        </div>
                        <div class="screen-item-link flex-1 text-sm md:text-base flex flex-wrap gap-1 md:gap-2">
                            <a href="/blog/tools.html" {if !input('category')}class="active"{/if}>All</a>
                            {volist name="blogCategory" id="vo"}
                            <a href="{:url('/blog/tools', ['category' => $vo.id])}"
                                class="{:input('category') == $vo.id ? 'active' : ''}"  data-id="{$vo.id}">{$vo.name}</a>
                            {/volist}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="screen-news-content-list max-w-9/10 mx-auto container">
            <!-- pc页面展示数据方式 分页 -->
            <div class="hidden md:block">
                <div class="flex flex-col gap-y-2 mb-10 md:grid md:grid-cols-2 md:gap-5">
                    {volist name="blogs" id="vo"}
                    <div class="screen-news-content-item bg-[#fff] rounded-sm flex flex-row gap-x-3 p-2.5 md:gap-x-[30px] md:py-[20px] md:px-[28px] md:h-[150px]">
                        <div class="screen-item-img w-1/3 md:w-[167px] md:h-[110px] overflow-hidden">
                            <a href="/blog/{$vo.seo_url}.html">
                                <img src="{$vo.image}" alt="img" class="h-full object-cover rounded-sm" />
                            </a>
                        </div>
                        <div class="screen-news-content-item-text flex-1 min-h-20 md:min-h-[110px]">
                            <div class="screen-title">
                                <h3 class="text-base text-[#002858] bold-text md:text-xl line-clamp-1 md:mb-2">
                                    <a href="/blog/{$vo.seo_url}.html">{$vo.title|strip_tags}</a>
                                </h3>
                            </div>
                            <div class="min-h-4">
                                <a href="/blog/{$vo.seo_url}.html">
                                    <p class="text-sm text-[#666666] md:text-base line-clamp-3">
                                        {$vo.content|strip_tags}
                                    </p>
                                </a>
                            </div>
                        </div>
                    </div>
                    {/volist}
                </div>
                <!-- 分页 -->
                <div class="pagination flex flex-row items-center justify-between gap-x-2 md:gap-x-3 md:justify-end">
                    {$blogs|raw}
                </div>
            </div>
            <!-- 移动端页面展示数据方式 点击追加 加载数据-->
            <div class=" md:hidden">
                <div class="flex flex-col gap-y-2 mb-10 md:grid md:grid-cols-2 md:gap-5" id="tools_news">
                    {volist name="blogs" id="vo"}
                    <div class="screen-news-content-item bg-[#fff] rounded-sm flex flex-row gap-x-3 p-2.5 md:gap-x-[30px] md:py-[20px] md:px-[28px] md:h-[150px]">
                        <div class="screen-item-img w-1/3 md:w-[167px] md:h-[110px] overflow-hidden">
                            <a href="/blog/{$vo.seo_url}.html">
                                <img src="{$vo.image}" alt="img" class="h-full object-cover rounded-sm" />
                            </a>
                        </div>
                        <div class="screen-news-content-item-text flex-1 min-h-20 md:min-h-[110px]">
                            <div class="screen-title">
                                <h3 class="text-base text-[#002858] bold-text md:text-xl line-clamp-1 md:mb-2">
                                    <a href="/blog/{$vo.seo_url}.html">{$vo.title|strip_tags}</a>
                                </h3>
                            </div>
                            <div class="min-h-4">
                                <a href="/blog/{$vo.seo_url}.html">
                                    <p class="text-sm text-[#666666] md:text-base line-clamp-3">
                                        {$vo.content|strip_tags}
                                    </p>
                                </a>
                            </div>
                        </div>
                    </div>
                    {/volist}
                </div>
                <!-- 分页 -->
                <div class="load-more-container text-center mb-10">
                    <button type="button" id="loadMoreBtn"
                        class="bg-[#2479ff] text-white px-8 py-2 rounded-sm hover:bg-[#015eea]">
                        加载更多
                    </button>
                    <p id="noMoreData" class="text-[#666666] hidden">
                        没有更多数据了
                    </p>
                </div>
            </div>
        </div>
    </section>

    {include file="public:footer"}
    {include file="public:foot"}

    <script>
        var swiper = new Swiper(".OverseasSwiper", {
            slidesPerView: "auto",
            spaceBetween: 20, //两者之间的间距
            slidesPerView: 1.3,
            pagination: {
                el: ".overseas-swiper",
                clickable: true,
            },
            breakpoints: {
                768: {
                    slidesPerView: 4,
                },
            },
        });
    </script>

    <script>
        $(document).ready(function () {
            let page = 1;
            const pageSize = 10;

            // 模拟新闻数据
            function getMockNewsData(page, pageSize) {
                const totalNews = 25; // 总共25条新闻
                const startIndex = (page - 1) * pageSize;
                const remainingItems = totalNews - startIndex;
                const itemsToReturn = Math.min(pageSize, remainingItems);

                if (itemsToReturn <= 0) {
                    return [];
                }

                const mockData = [];
                for (let i = 0; i < itemsToReturn; i++) {
                    mockData.push({
                        title: `SEO optimization of article titles ${startIndex + i + 1}`,
                        image: '__IMG__/case_5.jpg',
                        description: `I believe that many SEO analysts are reluctant to change the title when  ${startIndex + i + 1}`
                    });
                }
                return mockData;
            }

            // 加载更多按钮点击事件
            $('#loadMoreBtn').click(function () {
                // 添加加载状态
                $(this).text('加载中...').prop('disabled', true);

                // 模拟异步请求
                setTimeout(() => {
                    const response = {
                        data: getMockNewsData(page + 1, pageSize)
                    };

                    if (response.data && response.data.length > 0) {
                        // 追加新闻数据
                        response.data.forEach(function (item) {
                            const newsHtml = `

                            <div class="screen-news-content-item bg-[#fff] rounded-sm flex flex-row gap-x-3 p-2.5 md:gap-x-[30px] md:py-[20px] md:px-[28px] md:h-[150px]">
                        <div class="screen-item-img w-1/3 md:w-[167px] md:h-[110px] overflow-hidden">
                            <a href="/blog/details.html">
                                <img src="${item.image}" alt="img" class="h-full object-cover rounded-sm">
                            </a>
                        </div>
                        <div class="screen-news-content-item-text flex-1 min-h-20 md:min-h-[110px]">
                            <div class="screen-title">
                                <h3 class="text-base text-[#002858] bold-text md:text-xl line-clamp-1 md:mb-2">
                                    <a href="/blog/details.html">${item.title}</a>
                                </h3>
                            </div>
                            <div class="min-h-4">
                                <a href="/blog/details.html">
                                    <p class="text-sm text-[#666666] md:text-base line-clamp-3">
                                        ${item.description}
                                    </p>
                                </a>
                            </div>
                        </div>
                    </div>
                            `;
                            $('#tools_news').append(newsHtml);
                        });

                        page++;

                        // 如果返回的数据少于pageSize，说明没有更多数据了
                        if (response.data.length < pageSize) {
                            $('#loadMoreBtn').hide();
                            $('#noMoreData').show();
                        }
                    } else {
                        $('#loadMoreBtn').hide();
                        $('#noMoreData').show();
                    }

                    // 恢复按钮状态
                    $('#loadMoreBtn').text('加载更多').prop('disabled', false);
                }, 1000); // 模拟1秒的加载时间
            });
        });
    </script>
</body>

</html>