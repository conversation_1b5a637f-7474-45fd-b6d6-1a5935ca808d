<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>添加本质区别</title>
    {include file="common/head" /}
    <style>
        .content-section { margin-bottom: 30px; }
        .section-title { font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #303133; }
    </style>
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>添加本质区别</span>
                <el-button style="float: right;" @click="goBack">返回列表</el-button>
            </div>

            <el-form ref="differenceForm" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入名称"></el-input>
                </el-form-item>

                <el-form-item label="图标">
                    <div class="file-upload-container icon-preview">
                        <input type="file" name="icon" accept="image/*">
                        <button type="button" class="upload-btn">选择图标</button>
                        <div class="file-preview"></div>
                    </div>
                </el-form-item>

                <el-form-item label="排序">
                    <el-input-number v-model="form.sort" :min="0" :max="999"></el-input-number>
                </el-form-item>

                <div class="content-section">
                    <div class="section-title">GEO内容</div>
                    <el-form-item>
                        <div class="ckeditor-textarea" data-name="geo_content"></div>
                    </el-form-item>
                </div>

                <div class="content-section">
                    <div class="section-title">SEO内容</div>
                    <el-form-item>
                        <div class="ckeditor-textarea" data-name="seo_content"></div>
                    </el-form-item>
                </div>

                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="submitting">提交</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    submitting: false,
                    geoContentEditor: null,
                    seoContentEditor: null,
                    form: {
                        name: '',
                        geo_content: '',
                        seo_content: '',
                        sort: 50,
                        iconPreview: ''
                    },
                    rules: {
                        name: [
                            { required: true, message: '请输入名称', trigger: 'blur' }
                        ]
                    }
                }
            },
            methods: {
                submitForm() {
                    this.$refs.differenceForm.validate((valid) => {
                        if (valid) {
                            // 使用统一的表单提交工具（自动获取编辑器内容）
                            window.submitFormUnified({
                                url: "{:url('differenceAdd')}",
                                formData: this.form,
                                fileFields: ['icon'],
                                redirectUrl: "{:url('differenceIndex')}",
                            });
                        }
                    });
                },
                goBack() {
                    window.location.href = "{:url('differenceIndex')}";
                }
            }
        });
    </script>
</body>
</html>
