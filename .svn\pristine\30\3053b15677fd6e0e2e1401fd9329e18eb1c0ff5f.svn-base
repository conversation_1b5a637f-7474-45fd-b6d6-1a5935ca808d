/**
 * 后台管理公共JavaScript库
 * 包含富文本编辑器、文件上传、表单提交等功能
 */

class AdminCommon {
    constructor() {
        this.editors = new Map(); // 存储编辑器实例
        this.fileUploads = new Map(); // 存储文件上传配置
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.initFileUploads();
    }

    /**
     * 初始化文件上传
     */
    initFileUploads() {
        // 查找所有文件上传容器
        const uploadContainers = document.querySelectorAll('.file-upload-container');

        uploadContainers.forEach(container => {
            const input = container.querySelector('input[type="file"]');
            const button = container.querySelector('.upload-btn');
            const preview = container.querySelector('.file-preview');

            if (input && button) {
                const name = input.getAttribute('name');

                // 绑定点击事件
                button.addEventListener('click', () => {
                    input.click();
                });

                // 绑定文件选择事件
                input.addEventListener('change', (event) => {
                    this.handleFileChange(event, name, preview);
                });

                this.fileUploads.set(name, {
                    input,
                    button,
                    preview,
                    file: null,
                    previewUrl: null
                });
            }
        });
    }

    /**
     * 处理文件选择
     */
    handleFileChange(event, name, preview) {
        const file = event.target.files[0];
        const uploadConfig = this.fileUploads.get(name);

        if (file && uploadConfig) {
            uploadConfig.file = file;

            // 如果是图片，显示预览
            if (file.type.startsWith('image/') && preview) {
                const previewUrl = URL.createObjectURL(file);
                uploadConfig.previewUrl = previewUrl;

                // 更新预览图片
                let img = preview.querySelector('img');
                if (!img) {
                    img = document.createElement('img');
                    img.className = 'preview-image';
                    preview.appendChild(img);
                }
                img.src = previewUrl;
                img.style.display = 'block';
            }
        }
    }

    /**
     * 获取上传的文件
     */
    getUploadFile(name) {
        const uploadConfig = this.fileUploads.get(name);
        return uploadConfig ? uploadConfig.file : null;
    }

    /**
     * 统一的表单提交函数
     * 支持编辑器内容自动获取和文件上传
     */
    async submitForm(options = {}) {
        const {
            url,
            method = 'POST',
            formData = {},
            fileFields = [],
            onSuccess = () => {},
            onError = () => {},
            onComplete = () => {},
            redirectUrl = null,
            autoGetEditorData = true  // 是否自动获取编辑器内容
        } = options;

        // 自动获取编辑器内容
        if (autoGetEditorData && window.ckEditorHelper) {
            console.log('开始获取编辑器内容...');

            // 先刷新所有编辑器，确保DOM变化被捕获
            if (typeof window.ckEditorHelper.refreshAllEditors === 'function') {
                window.ckEditorHelper.refreshAllEditors();
            }

            // 等待一小段时间让刷新生效
            await new Promise(resolve => setTimeout(resolve, 100));

            const editorData = window.ckEditorHelper.getAllData();
            console.log('获取到的编辑器数据:', editorData);
            Object.assign(formData, editorData);
        }

        // 创建FormData对象
        const data = new FormData();

        // 添加普通字段
        Object.keys(formData).forEach(key => {
            if (fileFields.includes(key) && formData[key] instanceof File) {
                // 如果是文件字段且已经是File对象，直接添加
                data.append(key, formData[key]);
            } else if (formData[key] !== null && formData[key] !== undefined) {
                // 普通字段
                data.append(key, formData[key]);
            }
        });

        // 添加文件（从文件上传组件获取）
        fileFields.forEach(fieldName => {
            // 避免重复添加已经在formData中的文件
            if (!(formData[fieldName] instanceof File)) {
                const file = this.getUploadFile(fieldName);
                if (file) {
                    data.append(fieldName, file);
                }
            }
        });

        // 发送请求
        return fetch(url, {
            method: method,
            body: data,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            // 检查响应状态
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 检查内容类型
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                // 如果不是JSON，尝试获取文本内容用于调试
                return response.text().then(text => {
                    console.error('服务器返回非JSON响应:', text.substring(0, 200));
                    throw new Error('服务器返回了非JSON格式的响应，可能是错误页面或重定向');
                });
            }

            return response.json();
        })
        .then(result => {
            console.log('服务器返回的完整响应:', result);
            console.log('响应代码:', result.code, '类型:', typeof result.code);
            console.log('响应消息:', result.msg);

            // ThinkPHP的success方法通常返回code: 1，但也可能是其他格式
            // 检查多种可能的成功标识
            const isSuccess = result.code === 1 || result.code === '1' ||
                             result.status === 1 || result.status === '1' ||
                             result.success === true || result.success === 'true';

            if (isSuccess) {
                console.log('判定为成功响应');
                // 成功
                if (typeof onSuccess === 'function') {
                    onSuccess(result);
                }

                // 显示成功消息
                if (window.Vue && window.Vue.prototype.$message) {
                    window.Vue.prototype.$message.success(result.msg || '操作成功');
                } else {
                    alert(result.msg || '操作成功');
                }

                // 重定向
                if (redirectUrl) {
                    setTimeout(() => {
                        window.location.href = redirectUrl;
                    }, 1000);
                }
            } else {
                console.log('判定为失败响应');
                // 失败
                if (typeof onError === 'function') {
                    onError(result);
                }

                // 显示错误消息
                if (window.Vue && window.Vue.prototype.$message) {
                    window.Vue.prototype.$message.error(result.msg || '操作失败');
                } else {
                    alert(result.msg || '操作失败');
                }
            }

            return result;
        })
        .catch(error => {
            console.error('Submit error:', error);

            if (typeof onError === 'function') {
                onError(error);
            }

            // 根据错误类型显示不同的错误消息
            let errorMessage = '提交失败，请重试';

            if (error.message) {
                if (error.message.includes('非JSON格式')) {
                    errorMessage = '服务器响应异常，请检查URL是否正确或联系管理员';
                } else if (error.message.includes('HTTP 404')) {
                    errorMessage = '请求的页面不存在，请检查URL';
                } else if (error.message.includes('HTTP 500')) {
                    errorMessage = '服务器内部错误，请联系管理员';
                } else if (error.message.includes('HTTP 403')) {
                    errorMessage = '没有权限访问，请重新登录';
                } else if (error.message.includes('Failed to fetch')) {
                    errorMessage = '网络连接失败，请检查网络连接';
                }
            }

            // 显示错误消息
            if (window.Vue && window.Vue.prototype.$message) {
                window.Vue.prototype.$message.error(errorMessage);
            } else {
                alert(errorMessage);
            }
        })
        .finally(() => {
            if (typeof onComplete === 'function') {
                onComplete();
            }
        });
    }

    /**
     * 销毁编辑器
     */
    destroyEditor(name) {
        const editor = this.editors.get(name);
        if (editor) {
            editor.destroy();
            this.editors.delete(name);
        }
    }

    /**
     * 销毁所有编辑器
     */
    destroyAllEditors() {
        this.editors.forEach((editor) => {
            editor.destroy();
        });
        this.editors.clear();
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.destroyAllEditors();

        // 清理文件预览URL
        this.fileUploads.forEach(config => {
            if (config.previewUrl) {
                URL.revokeObjectURL(config.previewUrl);
            }
        });
        this.fileUploads.clear();
    }
}

// 全局实例
window.AdminCommon = AdminCommon;

/**
 * 统一的表单提交函数（全局版本）
 * 直接使用AdminCommon实例的submitForm方法
 */
window.submitFormUnified = async function(options = {}) {
    if (window.adminCommon) {
        return await window.adminCommon.submitForm(options);
    } else {
        console.error('AdminCommon未初始化，无法提交表单');
        throw new Error('AdminCommon未初始化');
    }
};

/**
 * 全局工具函数集合
 * 提供Vue组件友好的API和通用工具方法
 */
window.AdminUtils = {
    // 自动初始化编辑器
    initEditors: function(selector = '.ckeditor-textarea') {
        if (window.ckEditorHelper) {
            setTimeout(() => {
                window.ckEditorHelper.initAll(selector)
                    .then(editors => {
                        console.log(`成功初始化 ${editors.length} 个编辑器`);
                    })
                    .catch(error => {
                        console.error('编辑器初始化失败:', error);
                    });
            }, 100);
        }
    },

    // 通用表单提交（Vue组件友好的API）
    submitForm: function(vueInstance, options) {
        const {
            formRef = 'form',
            url,
            formData,
            fileFields = [],
            redirectUrl,
            successMessage = '操作成功',
            errorMessage = '操作失败',
            autoGetEditorData = true,  // 是否自动获取编辑器内容
            beforeSubmit = null,  // 提交前的回调函数
            onComplete = null  // 完成后的回调函数
        } = options;

        // 防止重复提交
        if (vueInstance.submitting) {
            console.log('表单正在提交中，请勿重复操作');
            return;
        }

        vueInstance.$refs[formRef].validate(async (valid) => {
            if (valid) {
                // 设置提交状态（只在这里设置一次）
                vueInstance.submitting = true;

                try {
                    // 如果有beforeSubmit回调，先执行它
                    if (beforeSubmit && typeof beforeSubmit === 'function') {
                        beforeSubmit(formData);
                    }

                    // 使用统一的表单提交函数
                    await window.submitFormUnified({
                        url: url,
                        formData: formData,
                        fileFields: fileFields,
                        redirectUrl: redirectUrl,
                        autoGetEditorData: autoGetEditorData,
                        onSuccess: (result) => {
                            vueInstance.$message.success(result.msg || successMessage);
                        },
                        onError: (error) => {
                            vueInstance.$message.error(error.msg || errorMessage);
                        },
                        onComplete: () => {
                            vueInstance.submitting = false;
                            // 执行自定义的完成回调
                            if (onComplete && typeof onComplete === 'function') {
                                onComplete();
                            }
                        }
                    });
                } catch (error) {
                    console.error('表单提交失败:', error);
                    vueInstance.submitting = false;
                    // 执行自定义的完成回调
                    if (onComplete && typeof onComplete === 'function') {
                        onComplete();
                    }
                }
            } else {
                console.log('表单验证失败');
            }
        });
    },

    // 获取指定编辑器的内容
    getEditorContent: function(editorName) {
        if (window.ckEditorHelper && typeof window.ckEditorHelper.getData === 'function') {
            const content = window.ckEditorHelper.getData(editorName);
            console.log(`获取编辑器 ${editorName} 内容:`, content ? content.substring(0, 100) + '...' : '空内容');
            return content;
        }
        console.warn(`无法获取编辑器 ${editorName} 的内容，ckEditorHelper不可用`);
        return null;
    },

    // 获取所有编辑器的内容
    getAllEditorContent: function() {
        if (window.ckEditorHelper && typeof window.ckEditorHelper.getAllData === 'function') {
            return window.ckEditorHelper.getAllData();
        }
        console.warn('无法获取所有编辑器内容，ckEditorHelper不可用');
        return {};
    },

    // 通用返回功能
    goBack: function(url) {
        if (url) {
            window.location.href = url;
        } else {
            window.history.back();
        }
    }
};

// 初始化AdminCommon
function initAdminCommon() {
    if (!window.adminCommon) {
        window.adminCommon = new AdminCommon();
        console.log('AdminCommon initialized successfully');
    }
}

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化AdminCommon（文件上传等功能）
    if (typeof initAdminCommon === 'function') {
        initAdminCommon();
    } else if (typeof AdminCommon !== 'undefined') {
        // 如果initAdminCommon函数不存在，直接初始化
        if (!window.adminCommon) {
            window.adminCommon = new AdminCommon();
            console.log('AdminCommon initialized successfully');
        }
    }

    // 检查页面是否有编辑器元素并初始化
    const editorElements = document.querySelectorAll('.ckeditor-textarea');
    if (editorElements.length > 0) {
        window.AdminUtils.initEditors();
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.adminCommon) {
        window.adminCommon.cleanup();
    }
});
