<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>编辑服务</title>
    {include file="common/head" /}
    <style>
        .form-section { margin-bottom: 30px; }
        .section-title { font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #303133; }
        .dynamic-item { border: 1px solid #dcdfe6; border-radius: 4px; padding: 15px; margin-bottom: 10px; position: relative; }
        .remove-btn { position: absolute; top: 10px; right: 10px; }
    </style>
</head>
<body>
    <div id="app" class="admin-container">
        <el-card>
            <div slot="header">
                <span>编辑服务</span>
                <el-button style="float: right;" @click="goBack">返回列表</el-button>
            </div>

            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="section-title">基本信息</div>

                    <el-form-item label="服务名称" prop="name">
                        <el-input v-model="form.name" placeholder="请输入服务名称"></el-input>
                    </el-form-item>

                    <el-form-item label="服务标题" prop="title">
                        <el-input v-model="form.title" placeholder="请输入服务标题"></el-input>
                    </el-form-item>

                    <el-form-item label="SEO URL" prop="seo_url">
                        <el-input v-model="form.seo_url" placeholder="留空自动生成"></el-input>
                    </el-form-item>

                    <el-form-item label="图标">
                        <div class="upload-demo">
                            <el-upload
                                ref="iconUpload"
                                action="#"
                                :auto-upload="false"
                                :show-file-list="false"
                                :on-change="handleIconChange"
                                accept="image/*">
                                <el-button size="small" type="primary">选择图标</el-button>
                            </el-upload>
                            <img v-if="form.iconPreview" :src="form.iconPreview" class="image-preview" />
                            <span v-if="form.icon && !form.iconPreview">当前图标: {{ form.icon }}</span>
                        </div>
                    </el-form-item>

                    <el-form-item label="背景图片">
                        <div class="upload-demo">
                            <el-upload
                                ref="backclothUpload"
                                action="#"
                                :auto-upload="false"
                                :show-file-list="false"
                                :on-change="handleBackclothChange"
                                accept="image/*">
                                <el-button size="small" type="primary">选择背景图片</el-button>
                            </el-upload>
                            <img v-if="form.backclothPreview" :src="form.backclothPreview" class="image-preview" />
                            <img v-else-if="form.backcloth" :src="form.backcloth" class="image-preview" />
                        </div>
                    </el-form-item>

                    <el-form-item label="服务图片">
                        <div class="upload-demo">
                            <el-upload
                                ref="imageUpload"
                                action="#"
                                :auto-upload="false"
                                :show-file-list="false"
                                :on-change="handleImageChange"
                                accept="image/*">
                                <el-button size="small" type="primary">选择服务图片</el-button>
                            </el-upload>
                            <img v-if="form.imagePreview" :src="form.imagePreview" class="image-preview" />
                            <img v-else-if="form.image" :src="form.image" class="image-preview" />
                        </div>
                    </el-form-item>

                    <el-form-item label="排序">
                        <el-input-number v-model="form.sort" :min="0" :max="999"></el-input-number>
                    </el-form-item>

                    <el-form-item label="推荐">
                        <el-switch v-model="form.is_recommend"></el-switch>
                    </el-form-item>

                    <el-form-item label="服务内容">
                        <div class="ckeditor-textarea" data-name="content"></div>
                    </el-form-item>
                </div>

                <!-- 服务策略 -->
                <div class="form-section">
                    <div class="section-title">
                        服务策略
                        <el-button type="primary" size="mini" @click="addStrategy" style="margin-left: 10px;">添加策略</el-button>
                    </div>

                    <div v-for="(strategy, index) in form.strategies" :key="'strategy-' + index" class="dynamic-item">
                        <el-button type="danger" size="mini" class="remove-btn" @click="removeStrategy(index)">删除</el-button>

                        <el-form-item :label="'策略标题 ' + (index + 1)">
                            <el-input v-model="strategy.title" placeholder="请输入策略标题"></el-input>
                        </el-form-item>

                        <el-form-item :label="'策略内容 ' + (index + 1)">
                            <div :id="'strategy-editor-' + index"></div>
                        </el-form-item>

                        <el-form-item :label="'排序 ' + (index + 1)">
                            <el-input-number v-model="strategy.sort" :min="0" :max="999"></el-input-number>
                        </el-form-item>
                    </div>
                </div>

                <!-- 服务优势 -->
                <div class="form-section">
                    <div class="section-title">
                        服务优势
                        <el-button type="primary" size="mini" @click="addAdvantage" style="margin-left: 10px;">添加优势</el-button>
                    </div>

                    <div v-for="(advantage, index) in form.advantages" :key="'advantage-' + index" class="dynamic-item">
                        <el-button type="danger" size="mini" class="remove-btn" @click="removeAdvantage(index)">删除</el-button>

                        <el-form-item :label="'优势图标 ' + (index + 1)">
                            <el-input v-model="advantage.icon" placeholder="请输入图标URL或上传图标"></el-input>
                        </el-form-item>

                        <el-form-item :label="'优势标题 ' + (index + 1)">
                            <el-input v-model="advantage.title" placeholder="请输入优势标题"></el-input>
                        </el-form-item>

                        <el-form-item :label="'优势内容 ' + (index + 1)">
                            <div :id="'advantage-editor-' + index"></div>
                        </el-form-item>

                        <el-form-item :label="'排序 ' + (index + 1)">
                            <el-input-number v-model="advantage.sort" :min="0" :max="999"></el-input-number>
                        </el-form-item>
                    </div>
                </div>

                <!-- 服务流程 -->
                <div class="form-section">
                    <div class="section-title">
                        服务流程
                        <el-button type="primary" size="mini" @click="addProcess" style="margin-left: 10px;">添加流程</el-button>
                    </div>

                    <div v-for="(process, index) in form.processes" :key="'process-' + index" class="dynamic-item">
                        <el-button type="danger" size="mini" class="remove-btn" @click="removeProcess(index)">删除</el-button>

                        <el-form-item :label="'流程标题 ' + (index + 1)">
                            <el-input v-model="process.title" placeholder="请输入流程标题"></el-input>
                        </el-form-item>

                        <el-form-item :label="'流程内容 ' + (index + 1)">
                            <div :id="'process-editor-' + index"></div>
                        </el-form-item>

                        <el-form-item :label="'排序 ' + (index + 1)">
                            <el-input-number v-model="process.sort" :min="0" :max="999"></el-input-number>
                        </el-form-item>
                    </div>
                </div>

                <el-form-item>
                    <el-button type="primary" @click="submitForm" :loading="submitting">提交</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    {include file="common/foot" /}
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    submitting: false,
                    form: {
                        id: <?php echo $getone['id']; ?>,
                        name: '<?php echo $getone['name'] ?? ''; ?>',
                        title: '<?php echo $getone['title'] ?? ''; ?>',
                        seo_url: '<?php echo $getone['seo_url'] ?? ''; ?>',
                        content: `<?php echo addslashes($getone['content'] ?? ''); ?>`,
                        icon: '<?php echo $getone['icon'] ?? ''; ?>',
                        backcloth: '<?php echo $getone['backcloth'] ?? ''; ?>',
                        image: '<?php echo $getone['image'] ?? ''; ?>',
                        sort: <?php echo $getone['sort'] ?? 50; ?>,
                        is_recommend: <?php echo $getone['is_recommend'] ? 'true' : 'false'; ?>,
                        iconPreview: '',
                        backclothPreview: '',
                        imagePreview: '',
                        strategies: <?php echo json_encode($strategies ?? []); ?>,
                        advantages: <?php echo json_encode($advantages ?? []); ?>,
                        processes: <?php echo json_encode($processes ?? []); ?>
                    },
                    rules: {
                        name: [
                            { required: true, message: '请输入服务名称', trigger: 'blur' }
                        ],
                        title: [
                            { required: true, message: '请输入服务标题', trigger: 'blur' }
                        ]
                    }
                }
            },
            methods: {
                handleIconChange(file) {
                    this.form.iconFile = file.raw;
                    this.form.iconPreview = URL.createObjectURL(file.raw);
                },
                handleBackclothChange(file) {
                    this.form.backclothFile = file.raw;
                    this.form.backclothPreview = URL.createObjectURL(file.raw);
                },
                handleImageChange(file) {
                    this.form.imageFile = file.raw;
                    this.form.imagePreview = URL.createObjectURL(file.raw);
                },
                addStrategy() {
                    this.form.strategies.push({
                        title: '',
                        content: '',
                        sort: 50
                    });
                },
                removeStrategy(index) {
                    this.form.strategies.splice(index, 1);
                },
                addAdvantage() {
                    this.form.advantages.push({
                        icon: '',
                        title: '',
                        content: '',
                        sort: 50
                    });
                },
                removeAdvantage(index) {
                    this.form.advantages.splice(index, 1);
                },
                addProcess() {
                    this.form.processes.push({
                        title: '',
                        content: '',
                        sort: 50
                    });
                },
                removeProcess(index) {
                    this.form.processes.splice(index, 1);
                },
                submitForm() {
                    window.AdminUtils.submitForm(this, {
                        url: "{:url('edit')}",
                        formData: this.form,
                        fileFields: ['icon', 'backcloth', 'image'],
                        redirectUrl: "{:url('index')}",
                        beforeSubmit: (formData) => {
                            // 在提交前处理关联数据
                            formData.strategies = JSON.stringify(this.form.strategies);
                            formData.advantages = JSON.stringify(this.form.advantages);
                            formData.processes = JSON.stringify(this.form.processes);
                        }
                    });
                },
                goBack() {
                    window.AdminUtils.goBack("{:url('index')}");
                }
            }
        });
    </script>
</body>
</html>
